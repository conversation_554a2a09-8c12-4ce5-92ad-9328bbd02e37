from script.Framework.framework import UIAutomationFramework
import time

def handle_alert(automation):
    """处理异常弹框的接口函数"""
    while automation.running:
        try:
            # 检查是否存在弹框（这里假设弹框上有确定按钮）
            if automation.find_and_click_image("alert_button.png"):
                automation.logger.info("Alert handled")
        except Exception as e:
            automation.logger.error(f"Error handling alert: {e}")
        # 每隔1秒检查一次
        time.sleep(1)