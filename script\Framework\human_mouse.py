import pyautogui
import random
import time

def human_move_and_click(x, y, width=0, height=0, click_time='single', button='left', move_duration_range=(0.2, 0.6)):
    # 1. 隨機目標點（在圖片區域內）
    if width > 0 and height > 0:
        target_x = x + random.randint(int(width*0.2), int(width*0.8))
        target_y = y + random.randint(int(height*0.2), int(height*0.8))
    else:
        target_x, target_y = x, y

    # 2. 模擬人類鼠標移動（非直線、帶隨機延遲）
    duration = random.uniform(*move_duration_range)
    pyautogui.moveTo(target_x, target_y, duration=duration, tween=pyautogui.easeInOutQuad)

    # 3. 點擊前隨機停頓
    time.sleep(random.uniform(0.05, 0.25))

    # 4. 點擊（可選單擊/雙擊/右鍵）
    if click_time == 'double':
        pyautogui.click(target_x, target_y, clicks=2, interval=random.uniform(0.08, 0.18), button=button)
    elif click_time == 'right':
        pyautogui.click(target_x, target_y, button='right')
    else:
        pyautogui.click(target_x, target_y, clicks=1, button=button)

    # 5. 點擊後隨機停頓
    time.sleep(random.uniform(0.05, 0.18))
