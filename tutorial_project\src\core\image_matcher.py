"""
图像识别和匹配模块
提供基于OpenCV的图像识别、模板匹配、置信度设置等功能
"""

import cv2
import numpy as np
import pyautogui
from pathlib import Path
from typing import Tuple, Optional, List
from dataclasses import dataclass

from .logger import get_logger


@dataclass
class MatchResult:
    """图像匹配结果"""
    found: bool
    confidence: float
    center_x: int
    center_y: int
    top_left_x: int
    top_left_y: int
    width: int
    height: int


class ImageMatcher:
    """图像识别和匹配器"""
    
    def __init__(self, default_confidence=0.8):
        """
        初始化图像匹配器
        
        Args:
            default_confidence: 默认置信度阈值
        """
        self.default_confidence = default_confidence
        self.logger = get_logger("ImageMatcher")
        
        # 禁用PyAutoGUI的安全检查以提高性能
        pyautogui.FAILSAFE = False
        pyautogui.PAUSE = 0.1
    
    def capture_screen(self, region=None) -> np.ndarray:
        """
        截取屏幕图像
        
        Args:
            region: 截取区域 (x, y, width, height)
            
        Returns:
            屏幕图像的numpy数组
        """
        try:
            if region:
                screenshot = pyautogui.screenshot(region=region)
            else:
                screenshot = pyautogui.screenshot()
            
            # 转换为OpenCV格式
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            return screenshot_cv
            
        except Exception as e:
            self.logger.error(f"截取屏幕失败: {e}")
            return None
    
    def load_template(self, template_path: str) -> Optional[np.ndarray]:
        """
        加载模板图像
        
        Args:
            template_path: 模板图像路径
            
        Returns:
            模板图像的numpy数组
        """
        try:
            if not Path(template_path).exists():
                self.logger.error(f"模板图像不存在: {template_path}")
                return None
            
            template = cv2.imread(template_path, cv2.IMREAD_COLOR)
            if template is None:
                self.logger.error(f"无法加载模板图像: {template_path}")
                return None
            
            return template
            
        except Exception as e:
            self.logger.error(f"加载模板图像失败: {e}")
            return None
    
    def match_template(self, screenshot: np.ndarray, template: np.ndarray, 
                      confidence: float = None) -> MatchResult:
        """
        执行模板匹配
        
        Args:
            screenshot: 屏幕截图
            template: 模板图像
            confidence: 置信度阈值
            
        Returns:
            匹配结果
        """
        if confidence is None:
            confidence = self.default_confidence
        
        try:
            # 执行模板匹配
            result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            # 检查是否满足置信度要求
            if max_val >= confidence:
                # 计算匹配区域
                template_h, template_w = template.shape[:2]
                top_left_x, top_left_y = max_loc
                center_x = top_left_x + template_w // 2
                center_y = top_left_y + template_h // 2
                
                return MatchResult(
                    found=True,
                    confidence=max_val,
                    center_x=center_x,
                    center_y=center_y,
                    top_left_x=top_left_x,
                    top_left_y=top_left_y,
                    width=template_w,
                    height=template_h
                )
            else:
                return MatchResult(
                    found=False,
                    confidence=max_val,
                    center_x=0,
                    center_y=0,
                    top_left_x=0,
                    top_left_y=0,
                    width=0,
                    height=0
                )
                
        except Exception as e:
            self.logger.error(f"模板匹配失败: {e}")
            return MatchResult(False, 0.0, 0, 0, 0, 0, 0, 0)
    
    def find_image_on_screen(self, template_path: str, confidence: float = None,
                           region=None, timeout: int = 10) -> Optional[MatchResult]:
        """
        在屏幕上查找图像
        
        Args:
            template_path: 模板图像路径
            confidence: 置信度阈值
            region: 搜索区域
            timeout: 超时时间（秒）
            
        Returns:
            匹配结果，如果未找到返回None
        """
        if confidence is None:
            confidence = self.default_confidence
        
        # 加载模板图像
        template = self.load_template(template_path)
        if template is None:
            return None
        
        self.logger.info(f"开始查找图像: {Path(template_path).name}, 置信度: {confidence}")
        
        import time
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            # 截取屏幕
            screenshot = self.capture_screen(region)
            if screenshot is None:
                continue
            
            # 执行匹配
            result = self.match_template(screenshot, template, confidence)
            
            if result.found:
                self.logger.info(f"找到图像: {Path(template_path).name}, "
                               f"位置: ({result.center_x}, {result.center_y}), "
                               f"置信度: {result.confidence:.3f}")
                return result
            
            time.sleep(0.5)  # 短暂等待后重试
        
        self.logger.warning(f"未找到图像: {Path(template_path).name}, "
                          f"最高置信度: {result.confidence:.3f}")
        return None
    
    def find_multiple_images(self, template_paths: List[str], confidence: float = None,
                           region=None) -> List[MatchResult]:
        """
        同时查找多个图像
        
        Args:
            template_paths: 模板图像路径列表
            confidence: 置信度阈值
            region: 搜索区域
            
        Returns:
            匹配结果列表
        """
        if confidence is None:
            confidence = self.default_confidence
        
        results = []
        screenshot = self.capture_screen(region)
        
        if screenshot is None:
            return results
        
        for template_path in template_paths:
            template = self.load_template(template_path)
            if template is not None:
                result = self.match_template(screenshot, template, confidence)
                if result.found:
                    results.append(result)
        
        return results
    
    def wait_for_image(self, template_path: str, confidence: float = None,
                      timeout: int = 30, check_interval: float = 1.0) -> bool:
        """
        等待图像出现
        
        Args:
            template_path: 模板图像路径
            confidence: 置信度阈值
            timeout: 超时时间（秒）
            check_interval: 检查间隔（秒）
            
        Returns:
            是否找到图像
        """
        self.logger.info(f"等待图像出现: {Path(template_path).name}")
        
        import time
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            result = self.find_image_on_screen(template_path, confidence, timeout=1)
            if result and result.found:
                return True
            time.sleep(check_interval)
        
        self.logger.warning(f"等待图像超时: {Path(template_path).name}")
        return False
    
    def save_screenshot(self, filename: str, region=None):
        """
        保存屏幕截图
        
        Args:
            filename: 保存的文件名
            region: 截取区域
        """
        try:
            screenshot = self.capture_screen(region)
            if screenshot is not None:
                cv2.imwrite(filename, screenshot)
                self.logger.info(f"截图已保存: {filename}")
        except Exception as e:
            self.logger.error(f"保存截图失败: {e}")


# 示例使用
if __name__ == "__main__":
    # 创建图像匹配器
    matcher = ImageMatcher(default_confidence=0.8)
    
    # 测试截屏功能
    screenshot = matcher.capture_screen()
    if screenshot is not None:
        print(f"截图尺寸: {screenshot.shape}")
    
    # 保存测试截图
    matcher.save_screenshot("test_screenshot.png")
