[32m[2025-05-12 19:51:28][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-12 19:51:33][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-12 19:51:33][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-12 19:51:33][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-12 19:51:33][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-12 19:51:35][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-12 19:51:37][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-05-12 19:51:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:51:39][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-05-12 19:51:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:51:42][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-05-12 19:51:42][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:51:44][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-12 19:51:44][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:51:48][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-12 19:51:53][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-12 19:51:53][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-12 19:51:53][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-12 19:51:53][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-12 19:51:55][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-12 19:51:57][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-05-12 19:51:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:51:59][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-05-12 19:52:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:52:02][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-05-12 19:52:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:52:04][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-12 19:52:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:52:06][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-12 19:52:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 19:52:08][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-05-12 19:52:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:00:18][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-12 20:00:23][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-12 20:00:23][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-12 20:00:23][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-12 20:00:23][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-12 20:00:25][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-12 20:00:27][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-12 20:00:27][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-12 20:00:27][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-12 20:00:29][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-12 20:00:30][INFO][UIAutomation] Clicked at position (529, 211)[0m
[32m[2025-05-12 20:00:30][INFO][UIAutomation] Clicked center at (529, 211)[0m
[32m[2025-05-12 20:00:32][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-12 20:00:32][INFO][UIAutomation] Clicked at position (601, 294)[0m
[32m[2025-05-12 20:00:32][INFO][UIAutomation] Clicked center at (601, 294)[0m
[32m[2025-05-12 20:00:34][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-12 20:00:34][INFO][UIAutomation] Clicked at position (528, 233)[0m
[32m[2025-05-12 20:00:34][INFO][UIAutomation] Clicked center at (528, 233)[0m
[32m[2025-05-12 20:00:36][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-12 20:00:36][INFO][UIAutomation] Clicked at position (760, 394)[0m
[32m[2025-05-12 20:00:36][INFO][UIAutomation] Clicked center at (760, 394)[0m
[32m[2025-05-12 20:00:38][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-12 20:00:38][INFO][UIAutomation] Clicked at position (1177, 235)[0m
[32m[2025-05-12 20:00:38][INFO][UIAutomation] Clicked center at (1177, 235)[0m
[32m[2025-05-12 20:00:40][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-12 20:00:41][INFO][UIAutomation] Clicked at position (1354, 597)[0m
[32m[2025-05-12 20:00:41][INFO][UIAutomation] Clicked center at (1354, 597)[0m
[32m[2025-05-12 20:00:43][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-12 20:00:43][INFO][UIAutomation] Clicked at position (1337, 629)[0m
[32m[2025-05-12 20:00:43][INFO][UIAutomation] Clicked center at (1337, 629)[0m
[32m[2025-05-12 20:00:45][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-12 20:00:45][INFO][UIAutomation] Clicked at position (1308, 763)[0m
[32m[2025-05-12 20:00:45][INFO][UIAutomation] Clicked center at (1308, 763)[0m
[32m[2025-05-12 20:00:47][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-12 20:00:47][INFO][UIAutomation] Clicked at position (514, 253)[0m
[32m[2025-05-12 20:00:47][INFO][UIAutomation] Clicked center at (514, 253)[0m
[32m[2025-05-12 20:00:57][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-12 20:00:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:01:02][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-12 20:01:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:01:02][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-12 20:01:10][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:01:15][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-05-12 20:01:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:01:15][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:01:20][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-12 20:01:30][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-05-12 20:01:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:01:35][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:01:37][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-05-12 20:01:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:01:37][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:01:37][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-12 20:01:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-12 20:01:38][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-12 20:01:38][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-12 20:04:58][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-12 20:05:03][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-12 20:05:03][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-12 20:05:03][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-12 20:05:03][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-12 20:05:05][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-12 20:05:07][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-12 20:05:07][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-12 20:05:07][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-12 20:05:09][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-12 20:05:09][INFO][UIAutomation] Clicked at position (529, 211)[0m
[32m[2025-05-12 20:05:09][INFO][UIAutomation] Clicked center at (529, 211)[0m
[32m[2025-05-12 20:05:11][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-12 20:05:12][INFO][UIAutomation] Clicked at position (601, 294)[0m
[32m[2025-05-12 20:05:12][INFO][UIAutomation] Clicked center at (601, 294)[0m
[32m[2025-05-12 20:05:14][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-12 20:05:14][INFO][UIAutomation] Clicked at position (528, 233)[0m
[32m[2025-05-12 20:05:14][INFO][UIAutomation] Clicked center at (528, 233)[0m
[32m[2025-05-12 20:05:16][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-12 20:05:16][INFO][UIAutomation] Clicked at position (760, 394)[0m
[32m[2025-05-12 20:05:16][INFO][UIAutomation] Clicked center at (760, 394)[0m
[32m[2025-05-12 20:05:18][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-12 20:05:18][INFO][UIAutomation] Clicked at position (1177, 235)[0m
[32m[2025-05-12 20:05:18][INFO][UIAutomation] Clicked center at (1177, 235)[0m
[32m[2025-05-12 20:05:20][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-12 20:05:21][INFO][UIAutomation] Clicked at position (1354, 597)[0m
[32m[2025-05-12 20:05:21][INFO][UIAutomation] Clicked center at (1354, 597)[0m
[32m[2025-05-12 20:05:23][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-12 20:05:23][INFO][UIAutomation] Clicked at position (1337, 629)[0m
[32m[2025-05-12 20:05:23][INFO][UIAutomation] Clicked center at (1337, 629)[0m
[32m[2025-05-12 20:05:25][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-12 20:05:25][INFO][UIAutomation] Clicked at position (1308, 763)[0m
[32m[2025-05-12 20:05:25][INFO][UIAutomation] Clicked center at (1308, 763)[0m
[32m[2025-05-12 20:05:27][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-12 20:05:27][INFO][UIAutomation] Clicked at position (514, 253)[0m
[32m[2025-05-12 20:05:27][INFO][UIAutomation] Clicked center at (514, 253)[0m
[32m[2025-05-12 20:05:37][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-12 20:05:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:05:42][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-12 20:05:42][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:05:42][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-12 20:05:50][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:05:55][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-05-12 20:05:55][INFO][UIAutomation] Clicked at position (210, 390)[0m
[32m[2025-05-12 20:05:55][INFO][UIAutomation] Clicked top-left at (210, 390)[0m
[32m[2025-05-12 20:05:55][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:06:00][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-12 20:06:10][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-05-12 20:06:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:06:15][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:06:17][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-05-12 20:06:17][INFO][UIAutomation] Clicked at position (139, 213)[0m
[32m[2025-05-12 20:06:17][INFO][UIAutomation] Clicked top-left at (139, 213)[0m
[32m[2025-05-12 20:06:17][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:06:17][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-05-12 20:06:17][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-12 20:06:17][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-12 20:07:41][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-12 20:07:46][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-12 20:07:46][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-12 20:07:46][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-12 20:07:46][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-12 20:07:48][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-12 20:07:50][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-12 20:07:51][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-12 20:07:51][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-12 20:07:53][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-12 20:07:53][INFO][UIAutomation] Clicked at position (529, 211)[0m
[32m[2025-05-12 20:07:53][INFO][UIAutomation] Clicked center at (529, 211)[0m
[32m[2025-05-12 20:07:55][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-12 20:07:55][INFO][UIAutomation] Clicked at position (601, 294)[0m
[32m[2025-05-12 20:07:55][INFO][UIAutomation] Clicked center at (601, 294)[0m
[32m[2025-05-12 20:07:57][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-12 20:07:57][INFO][UIAutomation] Clicked at position (528, 233)[0m
[32m[2025-05-12 20:07:57][INFO][UIAutomation] Clicked center at (528, 233)[0m
[32m[2025-05-12 20:07:59][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-12 20:08:00][INFO][UIAutomation] Clicked at position (760, 394)[0m
[32m[2025-05-12 20:08:00][INFO][UIAutomation] Clicked center at (760, 394)[0m
[32m[2025-05-12 20:08:02][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-12 20:08:02][INFO][UIAutomation] Clicked at position (1177, 235)[0m
[32m[2025-05-12 20:08:02][INFO][UIAutomation] Clicked center at (1177, 235)[0m
[32m[2025-05-12 20:08:04][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-12 20:08:04][INFO][UIAutomation] Clicked at position (1354, 597)[0m
[32m[2025-05-12 20:08:04][INFO][UIAutomation] Clicked center at (1354, 597)[0m
[32m[2025-05-12 20:08:06][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-12 20:08:06][INFO][UIAutomation] Clicked at position (1337, 629)[0m
[32m[2025-05-12 20:08:06][INFO][UIAutomation] Clicked center at (1337, 629)[0m
[32m[2025-05-12 20:08:08][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-12 20:08:08][INFO][UIAutomation] Clicked at position (1308, 763)[0m
[32m[2025-05-12 20:08:08][INFO][UIAutomation] Clicked center at (1308, 763)[0m
[32m[2025-05-12 20:08:10][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-12 20:08:11][INFO][UIAutomation] Clicked at position (514, 253)[0m
[32m[2025-05-12 20:08:11][INFO][UIAutomation] Clicked center at (514, 253)[0m
[32m[2025-05-12 20:08:21][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-12 20:08:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:08:26][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-12 20:08:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:08:26][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-12 20:08:33][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:08:38][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-05-12 20:08:38][INFO][UIAutomation] Clicked at position (210, 390)[0m
[32m[2025-05-12 20:08:38][INFO][UIAutomation] Clicked top-left at (210, 390)[0m
[32m[2025-05-12 20:08:38][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:08:43][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-12 20:08:54][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-05-12 20:08:54][INFO][UIAutomation] Clicked at position (1494, 377)[0m
[32m[2025-05-12 20:08:54][INFO][UIAutomation] Clicked center at (1494, 377)[0m
[32m[2025-05-12 20:08:59][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:09:01][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-05-12 20:09:01][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:09:01][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:09:01][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-12 20:09:02][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-12 20:09:02][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-12 20:09:02][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-12 20:09:54][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-12 20:09:59][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-12 20:09:59][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-12 20:09:59][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-12 20:09:59][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-12 20:10:01][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-12 20:10:03][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-12 20:10:03][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-12 20:10:03][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-12 20:10:05][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-12 20:10:06][INFO][UIAutomation] Clicked at position (529, 211)[0m
[32m[2025-05-12 20:10:06][INFO][UIAutomation] Clicked center at (529, 211)[0m
[32m[2025-05-12 20:10:08][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-12 20:10:08][INFO][UIAutomation] Clicked at position (601, 294)[0m
[32m[2025-05-12 20:10:08][INFO][UIAutomation] Clicked center at (601, 294)[0m
[32m[2025-05-12 20:10:10][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-12 20:10:10][INFO][UIAutomation] Clicked at position (528, 233)[0m
[32m[2025-05-12 20:10:10][INFO][UIAutomation] Clicked center at (528, 233)[0m
[32m[2025-05-12 20:10:12][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-12 20:10:12][INFO][UIAutomation] Clicked at position (760, 394)[0m
[32m[2025-05-12 20:10:12][INFO][UIAutomation] Clicked center at (760, 394)[0m
[32m[2025-05-12 20:10:14][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-12 20:10:14][INFO][UIAutomation] Clicked at position (1177, 235)[0m
[32m[2025-05-12 20:10:14][INFO][UIAutomation] Clicked center at (1177, 235)[0m
[32m[2025-05-12 20:10:16][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-12 20:10:17][INFO][UIAutomation] Clicked at position (1354, 597)[0m
[32m[2025-05-12 20:10:17][INFO][UIAutomation] Clicked center at (1354, 597)[0m
[32m[2025-05-12 20:10:19][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-12 20:10:19][INFO][UIAutomation] Clicked at position (1337, 629)[0m
[32m[2025-05-12 20:10:19][INFO][UIAutomation] Clicked center at (1337, 629)[0m
[32m[2025-05-12 20:10:21][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-12 20:10:21][INFO][UIAutomation] Clicked at position (1308, 763)[0m
[32m[2025-05-12 20:10:21][INFO][UIAutomation] Clicked center at (1308, 763)[0m
[32m[2025-05-12 20:10:23][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-12 20:10:23][INFO][UIAutomation] Clicked at position (514, 253)[0m
[32m[2025-05-12 20:10:23][INFO][UIAutomation] Clicked center at (514, 253)[0m
[32m[2025-05-12 20:10:33][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-12 20:10:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:10:38][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-12 20:10:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:10:38][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-12 20:10:46][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:10:51][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-05-12 20:10:51][INFO][UIAutomation] Clicked at position (210, 390)[0m
[32m[2025-05-12 20:10:51][INFO][UIAutomation] Clicked top-left at (210, 390)[0m
[32m[2025-05-12 20:10:51][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:10:56][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-12 20:11:06][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-05-12 20:11:06][INFO][UIAutomation] Clicked at position (1494, 377)[0m
[32m[2025-05-12 20:11:06][INFO][UIAutomation] Clicked center at (1494, 377)[0m
[32m[2025-05-12 20:11:11][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:11:13][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-05-12 20:11:14][INFO][UIAutomation] Clicked at position (139, 213)[0m
[32m[2025-05-12 20:11:14][INFO][UIAutomation] Clicked top-left at (139, 213)[0m
[32m[2025-05-12 20:11:14][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:11:14][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-12 20:11:15][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-12 20:11:15][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-12 20:11:15][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-12 20:13:37][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-12 20:13:42][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-12 20:13:42][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-12 20:13:42][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-12 20:13:42][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-12 20:13:44][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-12 20:13:46][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-12 20:13:47][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-12 20:13:47][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-12 20:13:49][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-12 20:13:49][INFO][UIAutomation] Clicked at position (529, 211)[0m
[32m[2025-05-12 20:13:49][INFO][UIAutomation] Clicked center at (529, 211)[0m
[32m[2025-05-12 20:13:51][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-12 20:13:51][INFO][UIAutomation] Clicked at position (601, 294)[0m
[32m[2025-05-12 20:13:51][INFO][UIAutomation] Clicked center at (601, 294)[0m
[32m[2025-05-12 20:13:53][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-12 20:13:53][INFO][UIAutomation] Clicked at position (528, 233)[0m
[32m[2025-05-12 20:13:53][INFO][UIAutomation] Clicked center at (528, 233)[0m
[32m[2025-05-12 20:13:55][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-12 20:13:56][INFO][UIAutomation] Clicked at position (760, 394)[0m
[32m[2025-05-12 20:13:56][INFO][UIAutomation] Clicked center at (760, 394)[0m
[32m[2025-05-12 20:13:58][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-12 20:13:58][INFO][UIAutomation] Clicked at position (1177, 235)[0m
[32m[2025-05-12 20:13:58][INFO][UIAutomation] Clicked center at (1177, 235)[0m
[32m[2025-05-12 20:14:00][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-12 20:14:00][INFO][UIAutomation] Clicked at position (1354, 597)[0m
[32m[2025-05-12 20:14:00][INFO][UIAutomation] Clicked center at (1354, 597)[0m
[32m[2025-05-12 20:14:02][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-12 20:14:02][INFO][UIAutomation] Clicked at position (1337, 629)[0m
[32m[2025-05-12 20:14:02][INFO][UIAutomation] Clicked center at (1337, 629)[0m
[32m[2025-05-12 20:14:04][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-12 20:14:04][INFO][UIAutomation] Clicked at position (1308, 763)[0m
[32m[2025-05-12 20:14:04][INFO][UIAutomation] Clicked center at (1308, 763)[0m
[32m[2025-05-12 20:14:06][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-12 20:14:07][INFO][UIAutomation] Clicked at position (514, 253)[0m
[32m[2025-05-12 20:14:07][INFO][UIAutomation] Clicked center at (514, 253)[0m
[32m[2025-05-12 20:14:17][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-12 20:14:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:14:22][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-12 20:14:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-12 20:14:22][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-12 20:14:29][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:14:34][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-05-12 20:14:34][INFO][UIAutomation] Clicked at position (210, 390)[0m
[32m[2025-05-12 20:14:34][INFO][UIAutomation] Clicked top-left at (210, 390)[0m
[32m[2025-05-12 20:14:34][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:14:39][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-12 20:14:49][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-05-12 20:14:50][INFO][UIAutomation] Clicked at position (1494, 377)[0m
[32m[2025-05-12 20:14:50][INFO][UIAutomation] Clicked center at (1494, 377)[0m
[32m[2025-05-12 20:14:55][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-12 20:14:57][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-05-12 20:14:57][INFO][UIAutomation] Clicked at position (139, 213)[0m
[32m[2025-05-12 20:14:57][INFO][UIAutomation] Clicked top-left at (139, 213)[0m
[32m[2025-05-12 20:14:57][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-12 20:14:57][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-12 20:14:58][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-12 20:14:58][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-12 20:14:58][INFO][UIAutomation] UI automation framework finished successfully[0m
