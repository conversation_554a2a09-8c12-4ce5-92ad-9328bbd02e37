@echo off
echo ========================================
echo    UI自动化测试框架启动器
echo ========================================
echo.

echo [INFO] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装或未配置到PATH环境变量
    echo [INFO] 请先安装Python 3.8+
    pause
    exit /b 1
)

echo [INFO] Python环境检查通过
echo.

echo [INFO] 检查依赖包...
python -c "import pyautogui, cv2, colorlog" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] 依赖包未完全安装
    echo [INFO] 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] 依赖包安装失败
        pause
        exit /b 1
    )
)

echo [INFO] 依赖包检查通过
echo.

echo [INFO] 启动自动化测试框架...
echo [INFO] 3秒后开始执行...
timeout /t 3 >nul

python src/main.py

echo.
echo [INFO] 测试执行完成！
echo [INFO] 请查看logs目录下的日志文件获取详细信息
pause
