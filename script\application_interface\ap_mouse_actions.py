import pyautogui

from script.Framework.framework import UIAutomationFramework
import time
from script.application_interface.image_resources import image_resources
import os
# 导入检测器类
from script.Framework.framework import log_dir
from script.application_interface.visual_stability import VisualStabilityDetector
from script.application_interface.visual_stability import example_usage

def ap_task(automation):
    # automation.find_and_click_image(image_resources.ljw, click_time='single')
    # if not automation.get_yn_confirmation():
    #     print("操作已中止")
    #     exit()
    #
    # print("执行关键操作...")
    # time.sleep(2)
    # automation.find_click_and_type(image_resources.ljw, input_text='13.3', click_position='center')
    example_usage()

