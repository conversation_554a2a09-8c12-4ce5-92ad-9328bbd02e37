"""
全局配置设置
包含框架的各种配置参数和常量定义
"""

import os
from pathlib import Path
from typing import Dict, Any


class AutomationSettings:
    """自动化框架配置类"""
    
    # 项目根目录
    PROJECT_ROOT = Path(__file__).parent.parent
    
    # 目录配置
    RESOURCES_DIR = PROJECT_ROOT / "resources"
    IMAGES_DIR = RESOURCES_DIR / "images"
    TEMPLATES_DIR = RESOURCES_DIR / "templates"
    LOGS_DIR = PROJECT_ROOT / "logs"
    REPORTS_DIR = PROJECT_ROOT / "reports"
    CONFIG_DIR = PROJECT_ROOT / "config"
    
    # 确保目录存在
    @classmethod
    def ensure_directories(cls):
        """确保所有必要的目录存在"""
        directories = [
            cls.RESOURCES_DIR,
            cls.IMAGES_DIR,
            cls.TEMPLATES_DIR,
            cls.LOGS_DIR,
            cls.REPORTS_DIR,
            cls.CONFIG_DIR
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    # PyAutoGUI配置
    PYAUTOGUI_SETTINGS = {
        "FAILSAFE": True,           # 启用安全模式
        "PAUSE": 0.5,               # 操作间隔
        "MINIMUM_DURATION": 0.1,    # 最小操作持续时间
        "MINIMUM_SLEEP": 0.05       # 最小睡眠时间
    }
    
    # 图像识别配置
    IMAGE_RECOGNITION = {
        "DEFAULT_CONFIDENCE": 0.8,   # 默认置信度
        "DEFAULT_TIMEOUT": 10,       # 默认超时时间（秒）
        "RETRY_INTERVAL": 1.0,       # 重试间隔（秒）
        "MAX_RETRIES": 3,            # 最大重试次数
        "SCREENSHOT_ON_FAILURE": True # 失败时是否截图
    }
    
    # 任务管理配置
    TASK_MANAGEMENT = {
        "MAX_WORKERS": 3,            # 最大工作线程数
        "TASK_TIMEOUT": 300,         # 任务超时时间（秒）
        "QUEUE_TIMEOUT": 60,         # 队列超时时间（秒）
        "ENABLE_PRIORITY": True      # 启用优先级队列
    }
    
    # 日志配置
    LOGGING = {
        "LEVEL": "INFO",             # 日志级别
        "MAX_FILE_SIZE": 10 * 1024 * 1024,  # 最大文件大小（10MB）
        "BACKUP_COUNT": 5,           # 备份文件数量
        "DATE_FORMAT": "%Y-%m-%d %H:%M:%S",  # 日期格式
        "ENABLE_COLOR": True,        # 启用彩色输出
        "LOG_TO_FILE": True,         # 记录到文件
        "LOG_TO_CONSOLE": True       # 输出到控制台
    }
    
    # 测试配置
    TESTING = {
        "DEFAULT_WAIT_TIME": 2.0,    # 默认等待时间
        "SCREENSHOT_ON_ERROR": True,  # 错误时截图
        "GENERATE_REPORT": True,     # 生成测试报告
        "REPORT_FORMAT": "HTML",     # 报告格式
        "PARALLEL_EXECUTION": False  # 并行执行测试
    }
    
    # 蓝牙测试配置
    BLUETOOTH_TEST = {
        "CONNECTION_TIMEOUT": 30,    # 连接超时时间
        "PAIRING_TIMEOUT": 60,       # 配对超时时间
        "AUDIO_TEST_DURATION": 30,   # 音频测试持续时间
        "SUPPORTED_CODECS": ["MSBC", "CVSD"],  # 支持的编解码器
        "DEFAULT_VOLUME": 15,        # 默认音量级别
        "QUALITY_THRESHOLD": 0.8     # 音质阈值
    }
    
    # 音频测试配置
    AUDIO_TEST = {
        "CALL_TIMEOUT": 60,          # 通话超时时间
        "QUALITY_CHECK_INTERVAL": 5, # 质量检查间隔
        "VOLUME_LEVELS": [5, 10, 15, 20],  # 测试音量级别
        "TEST_FREQUENCIES": [300, 1000, 3000],  # 测试频率
        "NOISE_THRESHOLD": -40,      # 噪声阈值（dB）
        "DISTORTION_THRESHOLD": 0.1  # 失真阈值
    }
    
    # 错误处理配置
    ERROR_HANDLING = {
        "MAX_RETRY_ATTEMPTS": 3,     # 最大重试次数
        "RETRY_DELAY": 1.0,          # 重试延迟
        "CAPTURE_SCREENSHOTS": True,  # 捕获错误截图
        "LOG_STACK_TRACE": True,     # 记录堆栈跟踪
        "CONTINUE_ON_ERROR": False   # 错误时是否继续
    }
    
    # 性能监控配置
    PERFORMANCE = {
        "MONITOR_CPU": True,         # 监控CPU使用率
        "MONITOR_MEMORY": True,      # 监控内存使用
        "MONITOR_DISK": False,       # 监控磁盘使用
        "SAMPLE_INTERVAL": 5,        # 采样间隔（秒）
        "ALERT_THRESHOLD": 80        # 告警阈值（%）
    }
    
    @classmethod
    def get_image_path(cls, image_name: str) -> str:
        """
        获取图像文件的完整路径
        
        Args:
            image_name: 图像文件名
            
        Returns:
            图像文件的完整路径
        """
        return str(cls.IMAGES_DIR / image_name)
    
    @classmethod
    def get_log_path(cls, log_name: str = None) -> str:
        """
        获取日志文件路径
        
        Args:
            log_name: 日志文件名
            
        Returns:
            日志文件路径
        """
        if log_name is None:
            from datetime import datetime
            log_name = f"automation_{datetime.now().strftime('%Y%m%d')}.log"
        
        return str(cls.LOGS_DIR / log_name)
    
    @classmethod
    def get_report_path(cls, report_name: str = None) -> str:
        """
        获取报告文件路径
        
        Args:
            report_name: 报告文件名
            
        Returns:
            报告文件路径
        """
        if report_name is None:
            from datetime import datetime
            report_name = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        
        return str(cls.REPORTS_DIR / report_name)
    
    @classmethod
    def load_from_file(cls, config_file: str = "test_config.json"):
        """
        从配置文件加载设置
        
        Args:
            config_file: 配置文件名
        """
        import json
        
        config_path = cls.CONFIG_DIR / config_file
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置
                for section, settings in config_data.items():
                    if hasattr(cls, section):
                        current_settings = getattr(cls, section)
                        if isinstance(current_settings, dict):
                            current_settings.update(settings)
                
                print(f"配置已从 {config_file} 加载")
                
            except Exception as e:
                print(f"加载配置文件失败: {e}")
        else:
            print(f"配置文件不存在: {config_file}")
    
    @classmethod
    def save_to_file(cls, config_file: str = "test_config.json"):
        """
        保存设置到配置文件
        
        Args:
            config_file: 配置文件名
        """
        import json
        
        # 收集所有配置
        config_data = {}
        for attr_name in dir(cls):
            if not attr_name.startswith('_') and attr_name.isupper():
                attr_value = getattr(cls, attr_name)
                if isinstance(attr_value, dict):
                    config_data[attr_name] = attr_value
        
        config_path = cls.CONFIG_DIR / config_file
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
            
            print(f"配置已保存到 {config_file}")
            
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    @classmethod
    def print_current_settings(cls):
        """打印当前配置"""
        print("=== 当前配置设置 ===")
        
        sections = [
            ("PyAutoGUI设置", cls.PYAUTOGUI_SETTINGS),
            ("图像识别设置", cls.IMAGE_RECOGNITION),
            ("任务管理设置", cls.TASK_MANAGEMENT),
            ("日志设置", cls.LOGGING),
            ("测试设置", cls.TESTING),
            ("蓝牙测试设置", cls.BLUETOOTH_TEST),
            ("音频测试设置", cls.AUDIO_TEST),
            ("错误处理设置", cls.ERROR_HANDLING),
            ("性能监控设置", cls.PERFORMANCE)
        ]
        
        for section_name, section_config in sections:
            print(f"\n{section_name}:")
            for key, value in section_config.items():
                print(f"  {key}: {value}")


# 环境变量配置
def load_environment_config():
    """从环境变量加载配置"""
    
    # 从环境变量更新配置
    if os.getenv("AUTOMATION_CONFIDENCE"):
        AutomationSettings.IMAGE_RECOGNITION["DEFAULT_CONFIDENCE"] = float(
            os.getenv("AUTOMATION_CONFIDENCE")
        )
    
    if os.getenv("AUTOMATION_TIMEOUT"):
        AutomationSettings.IMAGE_RECOGNITION["DEFAULT_TIMEOUT"] = int(
            os.getenv("AUTOMATION_TIMEOUT")
        )
    
    if os.getenv("AUTOMATION_LOG_LEVEL"):
        AutomationSettings.LOGGING["LEVEL"] = os.getenv("AUTOMATION_LOG_LEVEL")
    
    if os.getenv("AUTOMATION_MAX_WORKERS"):
        AutomationSettings.TASK_MANAGEMENT["MAX_WORKERS"] = int(
            os.getenv("AUTOMATION_MAX_WORKERS")
        )


# 初始化配置
def initialize_settings():
    """初始化配置系统"""
    # 确保目录存在
    AutomationSettings.ensure_directories()
    
    # 加载环境变量配置
    load_environment_config()
    
    # 尝试加载配置文件
    AutomationSettings.load_from_file()
    
    print("配置系统初始化完成")


# 示例使用
if __name__ == "__main__":
    # 初始化配置
    initialize_settings()
    
    # 打印当前配置
    AutomationSettings.print_current_settings()
    
    # 保存配置到文件
    AutomationSettings.save_to_file()
    
    # 获取路径示例
    print(f"\n图像路径示例: {AutomationSettings.get_image_path('test.png')}")
    print(f"日志路径示例: {AutomationSettings.get_log_path()}")
    print(f"报告路径示例: {AutomationSettings.get_report_path()}")
