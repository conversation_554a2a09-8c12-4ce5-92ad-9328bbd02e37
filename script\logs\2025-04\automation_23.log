[32m[2025-04-23 12:31:48][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 12:31:53][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 12:31:53][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 12:31:53][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 12:31:53][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 12:31:53][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 12:31:55][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 12:31:55][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 12:31:55][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 12:31:57][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 12:31:58][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 12:31:58][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 12:32:00][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 12:32:00][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 12:32:00][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 12:32:02][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 12:32:02][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 12:32:02][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 12:32:04][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 12:32:04][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 12:32:04][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 12:32:06][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 12:32:06][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 12:32:06][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 12:32:08][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 12:32:09][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 12:32:09][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 12:32:11][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 12:32:11][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 12:32:11][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 12:32:13][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 12:32:13][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 12:32:13][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 12:32:15][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 12:32:15][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 12:32:15][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 12:32:20][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-23 12:32:21][INFO][UIAutomation] Clicked at position (1211, 344)[0m
[32m[2025-04-23 12:32:21][INFO][UIAutomation] Clicked center at (1211, 344)[0m
[32m[2025-04-23 12:32:26][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-23 12:32:26][INFO][UIAutomation] Clicked at position (1322, 344)[0m
[32m[2025-04-23 12:32:26][INFO][UIAutomation] Clicked center at (1322, 344)[0m
[32m[2025-04-23 12:32:26][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 12:32:33][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-23 12:32:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:32:33][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 12:32:38][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 12:32:39][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 12:32:39][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 12:32:39][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 12:33:37][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 12:33:42][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 12:33:42][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 12:33:42][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 12:33:42][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 12:33:42][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 12:33:44][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 12:33:44][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:33:46][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 12:33:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:33:48][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 12:33:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:33:50][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 12:33:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:33:52][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 12:33:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:33:54][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 12:33:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:33:56][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 12:33:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:33:58][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-23 12:33:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:34:01][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-23 12:34:01][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:34:20][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 12:34:25][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 12:34:25][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 12:34:25][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 12:34:25][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 12:34:25][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 12:34:27][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 12:34:28][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 12:34:28][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 12:34:30][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 12:34:30][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 12:34:30][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 12:34:32][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 12:34:32][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 12:34:32][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 12:34:34][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 12:34:34][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 12:34:34][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 12:34:36][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 12:34:36][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 12:34:36][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 12:34:38][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 12:34:39][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 12:34:39][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 12:34:41][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 12:34:41][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 12:34:41][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 12:34:43][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 12:34:43][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 12:34:43][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 12:34:45][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 12:34:45][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 12:34:45][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 12:34:47][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 12:34:47][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 12:34:47][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 12:34:52][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 12:34:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:34:58][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 12:34:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:34:58][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 12:35:05][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-23 12:35:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:35:05][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 12:35:10][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-23 12:38:13][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 12:38:18][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 12:38:18][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 12:38:18][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 12:38:18][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 12:38:18][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 12:38:20][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 12:38:20][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 12:38:20][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 12:38:22][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 12:38:22][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 12:38:22][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 12:38:24][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 12:38:24][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 12:38:24][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 12:38:26][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 12:38:27][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 12:38:27][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 12:38:29][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 12:38:29][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 12:38:29][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 12:38:31][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 12:38:31][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 12:38:31][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 12:38:33][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 12:38:33][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 12:38:33][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 12:38:35][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 12:38:35][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 12:38:35][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 12:38:37][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 12:38:38][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 12:38:38][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 12:38:40][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 12:38:40][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 12:38:40][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 12:38:45][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 12:38:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:38:50][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 12:38:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:38:50][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 12:38:57][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 12:38:58][INFO][UIAutomation] Clicked at position (446, 259)[0m
[32m[2025-04-23 12:38:58][INFO][UIAutomation] Clicked top-left at (446, 259)[0m
[32m[2025-04-23 12:38:58][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 12:39:03][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 12:39:04][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 12:39:04][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 12:39:04][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 12:44:38][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 12:44:43][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 12:44:43][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 12:44:43][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 12:44:43][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 12:44:43][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 12:44:45][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 12:44:45][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 12:44:45][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 12:44:47][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 12:44:47][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 12:44:47][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 12:44:49][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 12:44:50][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 12:44:50][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 12:44:52][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 12:44:52][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 12:44:52][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 12:44:54][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 12:44:54][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 12:44:54][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 12:44:56][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 12:44:56][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 12:44:56][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 12:44:58][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 12:44:58][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 12:44:58][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 12:45:00][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 12:45:01][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 12:45:01][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 12:45:03][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 12:45:03][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 12:45:03][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 12:45:05][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 12:45:05][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 12:45:05][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 12:45:10][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 12:45:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:45:15][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 12:45:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:45:15][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 12:45:23][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 12:45:23][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 12:45:23][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 12:45:23][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 12:45:28][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-23 12:45:28][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 12:45:28][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 12:54:55][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 12:55:00][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 12:55:00][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 12:55:00][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 12:55:00][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 12:55:00][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 12:55:02][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 12:55:03][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 12:55:03][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 12:55:05][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 12:55:05][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 12:55:05][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 12:55:07][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 12:55:07][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 12:55:07][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 12:55:09][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 12:55:09][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 12:55:09][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 12:55:11][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 12:55:12][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 12:55:12][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 12:55:14][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 12:55:14][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 12:55:14][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 12:55:16][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 12:55:16][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 12:55:16][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 12:55:18][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 12:55:18][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 12:55:18][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 12:55:20][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 12:55:20][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 12:55:20][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 12:55:22][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 12:55:23][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 12:55:23][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 12:55:28][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 12:55:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:55:33][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 12:55:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:55:33][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 12:55:40][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 12:55:40][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 12:55:40][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 12:55:40][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 12:55:45][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 12:55:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 12:55:46][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 12:55:46][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 12:58:20][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 12:58:25][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 12:58:25][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 12:58:25][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 12:58:25][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 12:58:25][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 12:58:27][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 12:58:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:58:29][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 12:58:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:58:31][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 12:58:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:58:33][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 12:58:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:58:36][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 12:58:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:58:38][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 12:58:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:58:40][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 12:58:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:58:45][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 12:58:50][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 12:58:50][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 12:58:50][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 12:58:50][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 12:58:50][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 12:58:52][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 12:58:52][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 12:58:52][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 12:58:54][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 12:58:54][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 12:58:54][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 12:58:56][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 12:58:57][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 12:58:57][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 12:58:59][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 12:58:59][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 12:58:59][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 12:59:01][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 12:59:01][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 12:59:01][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 12:59:03][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 12:59:03][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 12:59:03][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 12:59:05][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 12:59:06][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 12:59:06][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 12:59:08][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 12:59:08][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 12:59:08][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 12:59:10][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 12:59:10][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 12:59:10][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 12:59:12][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 12:59:12][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 12:59:12][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 12:59:17][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 12:59:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:59:22][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 12:59:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:59:22][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 12:59:30][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-23 12:59:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 12:59:30][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 12:59:35][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 12:59:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 12:59:36][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 12:59:36][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:00:56][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:01:01][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:01:01][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:01:01][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:01:01][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 13:01:01][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:01:03][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:01:03][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:01:03][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:01:05][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:01:05][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:01:05][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:01:07][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:01:08][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:01:08][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:01:10][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:01:10][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:01:10][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:01:12][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:01:12][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:01:12][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:01:14][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:01:14][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:01:14][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:01:16][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:01:17][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:01:17][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:01:19][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:01:19][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:01:19][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:01:21][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:01:21][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:01:21][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:01:23][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:01:23][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:01:23][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:01:28][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-23 13:01:28][INFO][UIAutomation] Clicked at position (1211, 344)[0m
[32m[2025-04-23 13:01:28][INFO][UIAutomation] Clicked center at (1211, 344)[0m
[32m[2025-04-23 13:01:33][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-23 13:01:34][INFO][UIAutomation] Clicked at position (1322, 344)[0m
[32m[2025-04-23 13:01:34][INFO][UIAutomation] Clicked center at (1322, 344)[0m
[32m[2025-04-23 13:01:34][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:01:41][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-23 13:01:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:01:41][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:01:46][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 13:01:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 13:01:47][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:01:47][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:04:23][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:04:28][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:04:28][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:04:28][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:04:28][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:04:30][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:04:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:32][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:04:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:34][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:04:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:36][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:04:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:38][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:04:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:40][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:04:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:43][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 13:04:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:45][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-23 13:04:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:47][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-23 13:04:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:49][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-23 13:04:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:54][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:04:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:59][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:04:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:04:59][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:05:06][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-23 13:05:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:05:06][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:05:14][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:05:19][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:05:19][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:05:19][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:05:19][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:05:21][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:05:21][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:05:21][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:05:23][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:05:23][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:05:23][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:05:25][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:05:25][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:05:25][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:05:27][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:05:28][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:05:28][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:05:30][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:05:30][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:05:30][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:05:32][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:05:32][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:05:32][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:05:34][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:05:34][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:05:34][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:05:36][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:05:36][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:05:36][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:05:38][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:05:39][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:05:39][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:05:41][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:05:41][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:05:41][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:05:46][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:05:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:05:51][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:05:51][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:05:51][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:05:58][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 13:05:59][INFO][UIAutomation] Clicked at position (210, 633)[0m
[32m[2025-04-23 13:05:59][INFO][UIAutomation] Clicked top-left at (210, 633)[0m
[32m[2025-04-23 13:05:59][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:06:04][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 13:06:05][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 13:06:05][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:06:05][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:08:44][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:08:49][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:08:49][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:08:49][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:08:49][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:08:51][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:08:51][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:08:51][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:08:53][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:08:54][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:08:54][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:08:56][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:08:56][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:08:56][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:08:58][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:08:58][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:08:58][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:09:00][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:09:00][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:09:00][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:09:02][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:09:03][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:09:03][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:09:05][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:09:05][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:09:05][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:09:07][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:09:07][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:09:07][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:09:09][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:09:09][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:09:09][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:09:11][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:09:11][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:09:11][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:09:16][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:09:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:09:21][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:09:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:09:22][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:09:29][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 13:09:29][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 13:09:29][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 13:09:29][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:09:34][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 13:09:35][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 13:09:35][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:09:35][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:14:12][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:14:17][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:14:17][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:14:17][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:14:17][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:14:19][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:14:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:21][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:14:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:23][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:14:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:25][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:14:25][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:27][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:14:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:29][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:14:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:31][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 13:14:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:33][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-23 13:14:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:36][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-23 13:14:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:38][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-23 13:14:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:43][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:14:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:48][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:14:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:14:48][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:15:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:15:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:15:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:15:06][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:15:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:15:08][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:15:08][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:15:08][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:15:10][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:15:10][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:15:10][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:15:12][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:15:12][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:15:12][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:15:14][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:15:14][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:15:14][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:15:16][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:15:17][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:15:17][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:15:19][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:15:19][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:15:19][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:15:21][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:15:21][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:15:21][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:15:23][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:15:23][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:15:23][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:15:25][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:15:26][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:15:26][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:15:28][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:15:28][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:15:28][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:15:33][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:15:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:15:38][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:15:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:15:38][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:15:45][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 13:15:45][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 13:15:45][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 13:15:45][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:15:56][INFO][UIAutomation] 单击Close[0m
[32m[2025-04-23 13:15:56][INFO][UIAutomation] Clicked at position (1520, 277)[0m
[32m[2025-04-23 13:15:56][INFO][UIAutomation] Clicked center at (1520, 277)[0m
[32m[2025-04-23 13:15:56][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 13:15:57][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 13:15:57][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:15:57][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:16:38][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:16:43][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:16:43][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:16:43][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:16:43][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:16:45][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:16:45][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:16:45][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:16:47][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:16:48][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:16:48][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:16:50][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:16:50][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:16:50][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:16:52][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:16:52][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:16:52][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:16:54][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:16:54][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:16:54][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:16:56][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:16:57][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:16:57][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:16:59][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:16:59][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:16:59][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:17:01][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:17:01][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:17:01][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:17:03][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:17:03][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:17:03][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:17:05][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:17:05][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:17:05][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:17:10][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:17:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:17:15][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:17:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:17:16][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:17:23][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 13:17:23][INFO][UIAutomation] Clicked at position (446, 507)[0m
[32m[2025-04-23 13:17:23][INFO][UIAutomation] Clicked top-left at (446, 507)[0m
[32m[2025-04-23 13:17:23][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:17:38][INFO][UIAutomation] 单击Close[0m
[31m[2025-04-23 13:17:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:17:38][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-23 13:17:38][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:17:38][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:21:22][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:21:27][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:21:27][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:21:27][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:21:27][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:21:29][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:21:30][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:21:30][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:21:32][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:21:32][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:21:32][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:21:34][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:21:34][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:21:34][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:21:36][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:21:36][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:21:36][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:21:38][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:21:38][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:21:38][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:21:40][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:21:41][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:21:41][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:21:43][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:21:43][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:21:43][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:21:45][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:21:45][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:21:45][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:21:47][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:21:47][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:21:47][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:21:49][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:21:49][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:21:49][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:21:54][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:21:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:21:59][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:22:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:22:00][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:22:07][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 13:22:07][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 13:22:07][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 13:22:07][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:22:22][INFO][UIAutomation] 单击Close[0m
[32m[2025-04-23 13:22:22][INFO][UIAutomation] Clicked at position (1520, 277)[0m
[32m[2025-04-23 13:22:22][INFO][UIAutomation] Clicked center at (1520, 277)[0m
[32m[2025-04-23 13:22:24][INFO][UIAutomation] 单击close1[0m
[31m[2025-04-23 13:22:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:22:24][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 13:22:25][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 13:22:25][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:22:25][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:23:06][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:23:11][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:23:11][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:23:11][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:23:11][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:23:13][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:23:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:23:15][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:23:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:23:17][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:23:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:24:28][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:24:33][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:24:33][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:24:33][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:24:33][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:24:36][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:24:36][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:24:36][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:24:38][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:24:38][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:24:38][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:24:40][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:24:40][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:24:40][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:24:42][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:24:42][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:24:42][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:24:44][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:24:45][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:24:45][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:24:47][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:24:47][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:24:47][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:24:49][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:24:49][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:24:49][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:24:51][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:24:51][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:24:51][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:24:53][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:24:53][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:24:53][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:24:55][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:24:56][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:24:56][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:25:01][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:25:01][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:25:06][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:25:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:25:06][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:25:13][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 13:25:13][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 13:25:13][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 13:25:13][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:25:28][INFO][UIAutomation] 单击Close[0m
[32m[2025-04-23 13:25:29][INFO][UIAutomation] Clicked at position (1520, 277)[0m
[32m[2025-04-23 13:25:29][INFO][UIAutomation] Clicked center at (1520, 277)[0m
[32m[2025-04-23 13:25:39][INFO][UIAutomation] 双击close1[0m
[31m[2025-04-23 13:25:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:25:39][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-23 13:25:39][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:25:39][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:27:22][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:27:27][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:27:27][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:27:27][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:27:27][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:27:29][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:27:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:27:31][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:27:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:27:33][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:27:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:27:35][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:27:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:27:37][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:27:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:27:49][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:27:54][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:27:54][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:27:54][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:27:54][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:27:56][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:27:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:27:58][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:27:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:28:01][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:28:01][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:28:03][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:28:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:28:05][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:28:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:28:07][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:28:21][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:28:26][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:28:26][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:28:26][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:28:26][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:28:28][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:28:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:28:31][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:28:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:28:33][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:28:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:28:35][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:28:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:29:26][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:29:31][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:29:31][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:29:31][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:29:31][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:29:33][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:29:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:29:35][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:29:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:29:37][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:29:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:29:39][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:29:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:29:41][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:29:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:29:43][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:29:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:30:18][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:30:23][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:30:23][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:30:23][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:30:23][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:30:25][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:30:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:30:28][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:30:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:30:30][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:30:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:30:32][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:30:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:30:34][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:30:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:30:36][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:30:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:30:38][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 13:30:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:30:40][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-23 13:30:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:31:55][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:32:00][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:32:00][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:32:00][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:32:00][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:32:02][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:32:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:04][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:32:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:06][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:32:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:08][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:32:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:10][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:32:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:13][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:32:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:15][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 13:32:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:17][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-23 13:32:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:19][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-23 13:32:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:21][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-23 13:32:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:26][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:32:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:31][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:32:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:31][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:32:38][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-23 13:32:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:32:39][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:33:11][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:33:16][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:33:16][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:33:16][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:33:16][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:33:18][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:33:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:33:20][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:33:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:33:22][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:33:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:33:25][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:33:25][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:33:27][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:33:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:33:29][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:33:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:33:31][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 13:33:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:33:33][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-23 13:33:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:34:56][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:35:01][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:35:01][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:35:01][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:35:01][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:35:03][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:35:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:05][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:35:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:07][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:35:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:32][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:35:37][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:35:37][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:35:37][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:35:37][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:35:39][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:35:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:41][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:35:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:43][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:35:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:45][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:35:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:47][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:35:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:49][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:35:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:51][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 13:35:51][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:53][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-23 13:35:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:35:56][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-23 13:35:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:37:02][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:37:07][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:37:07][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:37:07][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:37:07][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 13:37:07][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:37:09][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:37:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:37:12][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:37:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:37:14][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:37:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:37:16][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:37:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:37:18][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:37:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:37:20][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:39:03][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:39:08][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:39:08][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:39:08][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:39:08][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:39:08][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 13:39:10][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:39:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:39:12][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:39:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:39:14][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:39:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:39:17][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:39:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:39:19][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:39:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:39:21][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:39:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:39:23][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-23 13:39:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:40:55][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:41:00][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:41:00][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:41:00][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:41:00][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 13:41:00][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:41:02][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:41:02][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:41:02][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:41:04][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:41:04][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:41:04][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:41:06][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:41:06][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:41:06][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:41:08][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:41:09][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:41:09][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:41:11][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:41:11][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:41:11][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:41:13][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:41:13][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:41:13][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:41:15][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:41:15][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:41:15][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:41:17][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:41:17][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:41:17][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:41:19][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:41:20][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:41:20][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:41:22][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:41:22][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:41:22][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:41:27][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:41:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:41:32][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:41:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:41:32][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:41:39][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-23 13:41:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:41:39][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:41:54][INFO][UIAutomation] 单击Close[0m
[31m[2025-04-23 13:41:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:41:55][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 13:41:56][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 13:41:56][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:41:56][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:42:37][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:42:42][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:42:42][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:42:42][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:42:42][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:42:42][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 13:42:44][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:42:44][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:42:46][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:42:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:42:48][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 13:42:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:42:50][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 13:42:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:42:52][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 13:42:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:42:54][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 13:42:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:43:06][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:43:11][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:43:11][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:43:11][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:43:11][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:43:11][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 13:43:14][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 13:43:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:43:16][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 13:43:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:43:52][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:43:57][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:43:57][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:43:57][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:43:57][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 13:43:57][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:43:59][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:43:59][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:43:59][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:44:01][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:44:02][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:44:02][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:44:04][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:44:04][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:44:04][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:44:06][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:44:06][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:44:06][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:44:08][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:44:08][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:44:08][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:44:10][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:44:10][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:44:10][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:44:12][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:44:13][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:44:13][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:44:15][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:44:15][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:44:15][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:44:17][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:44:17][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:44:17][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:44:19][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:44:19][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:44:19][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:44:24][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:44:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:44:29][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:44:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:44:29][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:44:37][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-23 13:44:37][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 13:44:37][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 13:44:37][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:44:52][INFO][UIAutomation] 单击Close[0m
[32m[2025-04-23 13:44:52][INFO][UIAutomation] Clicked at position (1520, 277)[0m
[32m[2025-04-23 13:44:52][INFO][UIAutomation] Clicked center at (1520, 277)[0m
[32m[2025-04-23 13:44:52][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 13:44:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 13:44:53][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:44:53][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 13:49:54][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 13:49:59][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 13:49:59][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 13:49:59][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 13:49:59][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 13:49:59][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 13:50:01][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 13:50:01][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 13:50:01][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 13:50:03][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 13:50:04][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 13:50:04][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 13:50:06][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 13:50:06][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 13:50:06][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 13:50:08][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 13:50:08][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 13:50:08][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 13:50:10][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 13:50:10][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 13:50:10][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 13:50:12][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 13:50:12][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 13:50:12][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 13:50:14][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 13:50:15][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 13:50:15][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 13:50:17][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 13:50:17][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 13:50:17][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 13:50:19][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 13:50:19][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 13:50:19][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 13:50:21][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 13:50:21][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 13:50:21][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 13:50:26][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 13:50:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:50:31][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 13:50:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 13:50:31][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 13:50:39][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-23 13:50:39][INFO][UIAutomation] Clicked at position (446, 419)[0m
[32m[2025-04-23 13:50:39][INFO][UIAutomation] Clicked top-left at (446, 419)[0m
[32m[2025-04-23 13:50:39][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 13:50:44][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-23 13:50:54][INFO][UIAutomation] 单击Close[0m
[32m[2025-04-23 13:50:54][INFO][UIAutomation] Clicked at position (1520, 277)[0m
[32m[2025-04-23 13:50:54][INFO][UIAutomation] Clicked center at (1520, 277)[0m
[32m[2025-04-23 13:50:54][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-23 13:50:54][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 13:50:54][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 14:00:45][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:00:50][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:00:50][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:00:50][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:00:50][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:00:50][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:00:52][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 14:00:52][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 14:00:52][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 14:00:54][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 14:00:54][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 14:00:54][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 14:00:56][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 14:00:57][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 14:00:57][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 14:00:59][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 14:00:59][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 14:00:59][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 14:01:01][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 14:01:01][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 14:01:01][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 14:01:03][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 14:01:03][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 14:01:03][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 14:01:05][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 14:01:06][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 14:01:06][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 14:01:08][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 14:01:08][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 14:01:08][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 14:01:10][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 14:01:10][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 14:01:10][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 14:01:12][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 14:01:12][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 14:01:12][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 14:01:17][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-23 14:01:17][INFO][UIAutomation] Clicked at position (1211, 344)[0m
[32m[2025-04-23 14:01:17][INFO][UIAutomation] Clicked center at (1211, 344)[0m
[32m[2025-04-23 14:01:22][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-23 14:01:23][INFO][UIAutomation] Clicked at position (1322, 344)[0m
[32m[2025-04-23 14:01:23][INFO][UIAutomation] Clicked center at (1322, 344)[0m
[32m[2025-04-23 14:01:23][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 14:01:30][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-23 14:01:30][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 14:01:30][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 14:01:30][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 14:01:35][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-23 14:01:45][INFO][UIAutomation] 单击Close[0m
[31m[2025-04-23 14:01:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:01:45][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 14:01:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 14:01:46][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 14:01:46][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 14:03:13][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:03:18][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:03:18][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:03:18][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:03:18][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:03:18][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:03:20][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 14:03:20][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 14:03:20][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 14:03:22][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 14:03:22][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 14:03:22][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 14:03:24][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 14:03:24][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 14:03:24][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 14:03:26][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 14:03:27][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 14:03:27][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 14:03:29][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 14:03:29][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 14:03:29][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 14:03:31][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 14:03:31][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 14:03:31][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 14:03:33][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 14:03:33][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 14:03:33][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 14:03:35][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 14:03:35][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 14:03:35][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 14:03:37][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 14:03:38][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 14:03:38][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 14:03:40][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 14:03:40][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 14:03:40][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 14:03:45][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 14:03:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:03:50][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 14:03:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:03:50][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 14:03:57][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-23 14:03:57][INFO][UIAutomation] Clicked at position (446, 375)[0m
[32m[2025-04-23 14:03:57][INFO][UIAutomation] Clicked top-left at (446, 375)[0m
[32m[2025-04-23 14:03:57][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 14:04:02][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-23 14:04:13][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-23 14:04:13][INFO][UIAutomation] Clicked at position (1372, 324)[0m
[32m[2025-04-23 14:04:13][INFO][UIAutomation] Clicked center at (1372, 324)[0m
[32m[2025-04-23 14:04:13][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-23 14:04:13][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 14:04:13][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 14:06:05][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:06:10][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:06:10][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:06:10][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:06:10][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:06:10][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:06:12][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 14:06:12][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 14:06:12][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 14:06:14][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 14:06:15][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 14:06:15][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 14:06:17][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 14:06:17][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 14:06:17][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 14:06:19][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 14:06:19][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 14:06:19][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 14:06:21][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 14:06:21][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 14:06:21][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 14:06:23][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 14:06:23][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 14:06:23][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 14:06:25][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 14:06:26][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 14:06:26][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 14:06:28][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 14:06:28][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 14:06:28][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 14:06:30][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 14:06:30][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 14:06:30][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 14:06:32][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 14:06:32][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 14:06:32][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 14:06:37][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 14:06:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:06:42][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 14:06:42][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:06:42][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 14:06:50][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-23 14:06:50][INFO][UIAutomation] Clicked at position (446, 353)[0m
[32m[2025-04-23 14:06:50][INFO][UIAutomation] Clicked top-left at (446, 353)[0m
[32m[2025-04-23 14:06:50][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 14:06:55][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-23 14:07:05][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-23 14:07:05][INFO][UIAutomation] Clicked at position (1372, 324)[0m
[32m[2025-04-23 14:07:05][INFO][UIAutomation] Clicked center at (1372, 324)[0m
[32m[2025-04-23 14:07:15][INFO][UIAutomation] 取消所有勾选[0m
[32m[2025-04-23 14:07:15][INFO][UIAutomation] Clicked at position (527, 199)[0m
[32m[2025-04-23 14:07:15][INFO][UIAutomation] Clicked center at (527, 199)[0m
[32m[2025-04-23 14:07:15][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 14:07:16][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 14:07:16][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 14:07:16][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 14:14:02][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:14:07][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:14:07][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:14:07][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:14:07][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:14:07][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:14:09][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 14:14:09][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 14:14:09][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 14:14:11][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 14:14:11][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 14:14:11][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 14:14:13][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 14:14:13][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 14:14:13][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 14:14:16][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 14:14:16][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 14:14:16][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 14:14:18][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 14:14:18][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 14:14:18][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 14:14:20][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 14:14:20][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 14:14:20][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 14:14:22][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 14:14:22][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 14:14:22][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 14:14:24][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 14:14:25][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 14:14:25][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 14:14:27][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 14:14:27][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 14:14:27][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 14:14:29][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 14:14:29][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 14:14:29][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 14:14:34][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 14:14:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:14:39][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 14:14:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:14:39][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 14:14:46][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-23 14:14:47][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 14:14:47][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 14:14:47][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 14:14:52][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-23 14:15:02][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-23 14:15:02][INFO][UIAutomation] Clicked at position (1372, 324)[0m
[32m[2025-04-23 14:15:02][INFO][UIAutomation] Clicked center at (1372, 324)[0m
[32m[2025-04-23 14:15:07][INFO][UIAutomation] 快捷收起列表[0m
[32m[2025-04-23 14:15:12][INFO][UIAutomation] 取消所有勾选[0m
[31m[2025-04-23 14:15:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:15:12][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 14:15:13][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 14:15:13][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 14:15:13][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 14:16:32][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:16:37][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:16:37][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:16:37][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:16:37][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:16:37][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:16:39][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 14:16:39][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 14:16:39][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 14:16:41][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 14:16:41][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 14:16:41][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 14:16:43][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 14:16:43][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 14:16:43][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 14:16:45][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 14:16:46][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 14:16:46][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 14:16:48][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 14:16:48][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 14:16:48][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 14:16:50][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 14:16:50][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 14:16:50][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 14:16:52][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 14:16:52][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 14:16:52][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 14:16:54][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 14:16:54][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 14:16:54][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 14:16:56][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 14:16:57][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 14:16:57][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 14:16:59][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 14:16:59][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 14:16:59][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 14:17:04][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 14:17:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:17:09][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 14:17:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:17:09][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 14:17:16][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-23 14:17:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:17:16][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 14:17:21][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-23 14:17:31][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-04-23 14:17:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:17:37][INFO][UIAutomation] 快捷收起列表[0m
[32m[2025-04-23 14:17:37][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-23 14:17:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-23 14:17:38][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 14:17:38][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-23 14:18:34][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:18:39][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:18:39][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:18:39][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:18:39][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:18:39][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:18:41][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 14:18:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:18:43][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 14:18:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:18:45][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 14:18:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:18:47][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 14:18:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:18:49][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 14:18:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:18:51][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 14:18:51][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:18:56][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:19:01][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:19:01][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:19:01][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:19:01][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:19:01][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:19:03][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 14:19:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:19:06][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 14:19:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:19:26][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:19:31][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:19:31][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:19:31][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:19:31][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:19:31][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:19:34][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 14:19:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:19:36][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 14:19:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:19:38][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 14:19:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:19:57][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:20:02][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:20:02][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:20:02][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:20:02][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:20:02][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:20:04][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-23 14:20:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:20:06][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-23 14:20:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:20:08][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-23 14:20:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:20:11][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-23 14:20:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:20:13][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-23 14:20:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:20:15][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-23 14:20:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:20:47][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-23 14:20:52][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-23 14:20:52][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-23 14:20:52][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-23 14:20:52][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-23 14:20:52][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-23 14:20:55][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-23 14:20:55][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-23 14:20:55][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-23 14:20:57][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-23 14:20:57][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-23 14:20:57][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-23 14:20:59][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-23 14:20:59][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-23 14:20:59][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-23 14:21:01][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-23 14:21:01][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-23 14:21:01][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-23 14:21:03][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-23 14:21:04][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-23 14:21:04][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-23 14:21:06][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-23 14:21:06][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-23 14:21:06][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-23 14:21:08][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-23 14:21:08][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-23 14:21:08][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-23 14:21:10][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-23 14:21:10][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-23 14:21:10][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-23 14:21:12][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-23 14:21:12][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-23 14:21:12][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-23 14:21:14][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-23 14:21:15][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-23 14:21:15][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-23 14:21:20][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-23 14:21:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:21:25][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-23 14:21:25][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:21:25][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-23 14:21:32][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-23 14:21:32][INFO][UIAutomation] Clicked at position (446, 633)[0m
[32m[2025-04-23 14:21:32][INFO][UIAutomation] Clicked top-left at (446, 633)[0m
[32m[2025-04-23 14:21:32][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-23 14:21:37][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-23 14:21:47][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-23 14:21:48][INFO][UIAutomation] Clicked at position (1372, 324)[0m
[32m[2025-04-23 14:21:48][INFO][UIAutomation] Clicked center at (1372, 324)[0m
[32m[2025-04-23 14:21:53][INFO][UIAutomation] 取消所有勾选[0m
[31m[2025-04-23 14:21:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-23 14:21:53][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-23 14:21:53][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-23 14:21:53][INFO][UIAutomation] UI automation framework finished successfully[0m
