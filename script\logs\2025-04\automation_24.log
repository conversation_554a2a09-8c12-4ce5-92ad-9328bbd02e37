[32m[2025-04-24 09:45:45][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 09:45:50][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 09:45:50][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 09:45:50][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 09:45:50][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 09:45:50][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 09:45:52][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 09:45:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:45:54][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 09:45:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:45:56][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 09:45:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:45:58][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 09:45:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:00][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 09:46:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:02][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 09:46:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:04][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 09:46:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:07][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 09:46:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:09][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 09:46:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:11][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 09:46:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:16][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 09:46:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:21][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 09:46:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:21][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 09:46:28][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 09:46:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:28][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 09:46:33][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 09:46:43][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-04-24 09:46:44][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:46:44][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 09:46:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 09:46:45][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 09:46:45][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 09:48:16][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 09:48:21][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 09:48:21][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 09:48:21][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 09:48:21][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 09:48:21][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 09:48:23][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 09:48:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:25][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 09:48:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:28][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 09:48:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:30][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 09:48:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:32][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 09:48:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:34][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 09:48:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:36][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 09:48:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:38][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 09:48:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:40][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 09:48:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:42][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 09:48:42][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:52][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 09:48:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:57][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 09:48:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:48:58][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 09:49:05][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 09:49:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:49:05][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 09:49:10][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 09:49:20][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-04-24 09:49:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:49:20][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 09:49:21][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 09:49:21][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 09:49:21][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 09:52:04][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 09:52:09][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 09:52:09][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 09:52:09][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 09:52:09][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 09:52:09][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 09:52:11][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 09:52:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:52:13][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 09:52:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:52:15][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 09:52:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:52:17][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 09:52:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:52:19][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 09:52:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 09:52:21][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 09:52:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:13:51][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 10:13:56][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 10:13:56][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 10:13:56][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 10:13:56][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 10:13:56][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 10:13:58][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 10:13:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:00][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 10:14:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:02][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 10:14:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:04][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 10:14:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:06][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 10:14:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:08][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 10:14:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:10][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 10:14:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:13][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 10:14:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:15][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 10:14:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:17][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 10:14:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:14:27][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 10:14:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:50:23][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 10:50:28][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 10:50:28][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 10:50:28][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 10:50:28][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 10:50:28][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 10:50:30][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 10:50:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:50:32][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 10:50:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:50:34][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 10:50:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:07][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 10:51:12][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 10:51:12][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 10:51:12][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 10:51:12][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 10:51:12][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 10:51:14][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 10:51:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:16][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 10:51:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:18][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 10:51:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:20][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 10:51:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:43][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 10:51:48][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 10:51:48][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 10:51:48][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 10:51:48][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 10:51:48][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 10:51:50][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 10:51:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:52][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 10:51:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:54][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 10:51:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:56][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 10:51:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:51:59][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 10:51:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:52:19][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 10:52:24][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 10:52:29][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 10:52:29][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 10:52:29][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 10:52:29][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 10:52:29][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 10:52:31][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 10:52:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:52:33][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 10:52:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:52:35][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 10:52:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:52:37][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 10:52:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:52:39][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 10:52:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:53:10][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 10:53:15][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 10:53:15][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 10:53:15][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 10:53:15][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 10:53:15][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 10:53:17][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 10:53:17][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 10:53:17][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 10:53:19][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 10:53:19][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 10:53:19][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 10:53:21][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 10:53:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:53:23][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 10:53:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:53:25][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 10:53:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:53:28][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 10:53:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:53:30][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 10:53:30][INFO][UIAutomation] Clicked at position (1401, 421)[0m
[32m[2025-04-24 10:53:30][INFO][UIAutomation] Clicked center at (1401, 421)[0m
[32m[2025-04-24 10:53:32][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 10:53:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:53:34][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 10:53:34][INFO][UIAutomation] Clicked at position (991, 640)[0m
[32m[2025-04-24 10:53:34][INFO][UIAutomation] Clicked center at (991, 640)[0m
[32m[2025-04-24 10:53:36][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 10:53:36][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 10:53:36][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 10:53:46][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 10:53:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:53:51][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 10:53:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 10:53:52][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 10:59:50][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 10:59:55][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 10:59:55][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 10:59:55][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 10:59:55][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 10:59:55][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 10:59:57][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 10:59:57][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 10:59:57][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 10:59:59][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 10:59:59][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 10:59:59][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 11:00:01][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:00:01][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:00:03][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:00:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:00:05][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 11:00:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:00:07][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 11:00:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:00:09][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 11:00:10][INFO][UIAutomation] Clicked at position (1401, 421)[0m
[32m[2025-04-24 11:00:10][INFO][UIAutomation] Clicked center at (1401, 421)[0m
[32m[2025-04-24 11:00:12][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 11:00:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:00:14][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 11:00:14][INFO][UIAutomation] Clicked at position (991, 640)[0m
[32m[2025-04-24 11:00:14][INFO][UIAutomation] Clicked center at (991, 640)[0m
[32m[2025-04-24 11:00:16][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 11:00:16][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 11:00:16][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 11:04:00][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:04:05][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:04:05][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:04:05][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:04:05][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:04:05][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:04:07][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:04:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:09][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:04:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:11][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 11:04:32][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:04:37][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:04:37][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:04:37][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:04:37][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:04:37][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:04:39][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:04:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:41][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:04:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:43][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:04:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:45][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:04:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:47][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 11:04:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:49][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 11:04:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:51][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 11:04:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:54][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 11:04:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:56][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 11:04:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:04:58][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 11:04:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:08][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 11:05:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:13][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 11:05:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:13][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 11:05:17][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:05:22][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:05:22][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:05:22][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:05:22][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:05:22][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:05:24][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:05:25][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:27][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:05:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:29][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:05:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:44][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:05:49][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:05:49][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:05:49][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:05:49][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:05:49][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:05:52][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:05:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:54][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:05:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:56][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:05:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:05:58][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:05:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:00][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 11:06:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:13][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:06:18][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:06:18][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:06:18][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:06:18][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:06:18][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:06:20][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:06:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:22][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:06:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:24][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:06:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:40][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:06:45][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:06:45][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:06:45][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:06:45][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:06:45][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:06:47][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:06:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:49][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:06:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:51][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:06:51][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:53][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:06:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:06:55][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 11:06:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:23][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:08:28][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:08:28][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:08:28][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:08:28][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:08:28][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:08:30][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:08:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:32][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:08:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:34][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:08:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:36][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:08:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:38][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 11:08:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:40][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 11:08:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:42][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 11:08:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:45][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 11:08:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:47][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 11:08:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:49][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 11:08:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:08:59][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 11:08:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:09:04][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 11:09:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:09:04][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 11:09:11][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 11:09:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:09:11][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:09:16][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 11:09:26][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 11:09:27][INFO][UIAutomation] Clicked at position (1601, 179)[0m
[32m[2025-04-24 11:09:27][INFO][UIAutomation] Clicked center at (1601, 179)[0m
[32m[2025-04-24 11:09:27][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:09:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:09:28][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:09:28][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:13:14][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:13:19][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:13:19][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:13:19][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:13:19][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:13:19][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:13:21][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 11:13:21][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 11:13:21][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 11:13:23][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 11:13:23][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 11:13:23][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 11:13:25][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:13:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:13:28][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:13:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:13:30][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 11:13:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:13:32][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 11:13:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:13:34][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 11:13:34][INFO][UIAutomation] Clicked at position (1401, 421)[0m
[32m[2025-04-24 11:13:34][INFO][UIAutomation] Clicked center at (1401, 421)[0m
[32m[2025-04-24 11:13:36][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 11:13:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:13:38][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 11:13:38][INFO][UIAutomation] Clicked at position (991, 640)[0m
[32m[2025-04-24 11:13:38][INFO][UIAutomation] Clicked center at (991, 640)[0m
[32m[2025-04-24 11:13:40][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 11:13:41][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 11:13:41][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 11:13:51][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 11:13:51][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:13:56][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 11:13:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:13:56][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 11:14:03][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 11:14:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:14:03][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:14:08][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 11:14:18][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 11:14:18][INFO][UIAutomation] Clicked at position (1595, 197)[0m
[32m[2025-04-24 11:14:18][INFO][UIAutomation] Clicked center at (1595, 197)[0m
[32m[2025-04-24 11:14:18][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:14:19][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:14:19][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:14:19][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:16:06][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:16:11][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:16:11][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:16:11][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:16:11][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:16:11][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:16:13][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:16:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:16:15][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:16:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:16:17][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:16:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:16:19][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:16:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:17:02][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:17:07][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:17:07][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:17:07][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:17:07][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:17:07][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:17:09][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 11:17:09][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 11:17:09][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 11:17:11][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 11:17:12][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 11:17:12][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 11:17:14][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 11:17:14][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 11:17:14][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 11:17:16][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 11:17:16][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 11:17:16][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 11:17:18][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 11:17:18][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 11:17:18][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 11:17:20][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 11:17:20][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 11:17:20][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 11:17:22][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 11:17:23][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 11:17:23][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 11:17:25][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 11:17:25][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 11:17:25][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 11:17:27][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 11:17:27][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 11:17:27][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 11:17:29][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 11:17:29][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 11:17:29][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 11:17:39][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-24 11:17:39][INFO][UIAutomation] Clicked at position (1211, 344)[0m
[32m[2025-04-24 11:17:39][INFO][UIAutomation] Clicked center at (1211, 344)[0m
[32m[2025-04-24 11:17:44][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-24 11:17:45][INFO][UIAutomation] Clicked at position (1322, 344)[0m
[32m[2025-04-24 11:17:45][INFO][UIAutomation] Clicked center at (1322, 344)[0m
[32m[2025-04-24 11:17:45][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 11:17:52][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 11:17:52][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 11:17:52][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 11:17:52][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:17:57][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 11:18:07][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 11:18:07][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 11:18:07][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 11:18:07][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:18:08][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:18:08][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:18:08][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:19:39][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:19:44][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:19:44][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:19:44][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:19:44][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:19:44][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:19:46][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 11:19:46][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 11:19:46][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 11:19:48][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 11:19:48][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 11:19:48][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 11:19:50][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 11:19:50][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 11:19:50][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 11:19:52][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 11:19:53][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 11:19:53][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 11:19:55][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 11:19:55][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 11:19:55][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 11:19:57][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 11:19:57][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 11:19:57][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 11:19:59][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 11:19:59][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 11:19:59][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 11:20:01][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 11:20:01][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 11:20:01][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 11:20:03][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 11:20:04][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 11:20:04][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 11:20:06][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 11:20:06][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 11:20:06][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 11:20:16][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 11:20:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:20:21][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 11:20:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:20:21][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 11:20:28][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 11:20:28][INFO][UIAutomation] Clicked at position (210, 264)[0m
[32m[2025-04-24 11:20:28][INFO][UIAutomation] Clicked top-left at (210, 264)[0m
[32m[2025-04-24 11:20:28][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:20:33][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 11:20:43][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 11:20:44][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 11:20:44][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 11:20:46][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 11:20:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:20:46][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:20:46][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 11:20:46][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:20:46][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:24:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:24:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:24:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:24:06][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:24:06][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:24:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:24:08][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 11:24:09][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 11:24:09][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 11:24:11][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 11:24:11][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 11:24:11][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 11:24:13][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 11:24:13][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 11:24:13][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 11:24:15][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 11:24:15][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 11:24:15][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 11:24:17][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 11:24:17][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 11:24:17][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 11:24:19][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 11:24:20][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 11:24:20][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 11:24:22][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 11:24:22][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 11:24:22][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 11:24:24][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 11:24:24][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 11:24:24][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 11:24:26][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 11:24:26][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 11:24:26][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 11:24:28][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 11:24:28][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 11:24:28][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 11:24:38][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 11:24:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:24:44][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 11:24:44][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:24:44][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 11:24:51][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 11:24:51][INFO][UIAutomation] Clicked at position (210, 242)[0m
[32m[2025-04-24 11:24:51][INFO][UIAutomation] Clicked top-left at (210, 242)[0m
[32m[2025-04-24 11:24:51][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:24:56][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 11:25:06][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 11:25:06][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 11:25:06][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 11:25:08][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 11:25:09][INFO][UIAutomation] Clicked at position (211, 217)[0m
[32m[2025-04-24 11:25:09][INFO][UIAutomation] Clicked top-left at (211, 217)[0m
[32m[2025-04-24 11:25:09][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:25:09][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:25:10][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:25:10][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:25:10][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:29:55][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:30:00][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:30:00][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:30:00][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:30:00][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:30:00][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:30:02][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:30:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:30:05][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:30:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:30:40][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:30:45][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:30:45][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:30:45][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:30:45][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:30:45][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:30:47][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:30:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:30:49][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:30:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:30:51][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:30:51][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:30:53][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:30:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:30:55][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 11:30:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:30:58][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 11:30:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:31:00][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 11:31:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:31:02][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 11:31:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:33:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:33:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:33:06][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:33:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:33:06][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:33:09][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 11:33:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:11][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 11:33:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:13][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 11:33:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:15][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 11:33:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:17][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 11:33:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:19][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 11:33:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:21][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 11:33:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:23][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 11:33:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:33:53][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:33:58][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:33:58][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:33:58][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:33:58][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:33:58][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:34:00][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 11:34:00][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 11:34:00][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 11:34:02][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 11:34:02][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 11:34:02][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 11:34:04][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 11:34:04][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 11:34:04][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 11:34:06][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 11:34:07][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 11:34:07][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 11:34:09][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 11:34:09][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 11:34:09][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 11:34:11][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 11:34:11][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 11:34:11][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 11:34:13][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 11:34:13][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 11:34:13][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 11:34:15][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 11:34:15][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 11:34:15][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 11:34:17][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 11:34:18][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 11:34:18][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 11:34:20][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 11:34:20][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 11:34:20][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 11:34:30][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-24 11:34:30][INFO][UIAutomation] Clicked at position (1211, 344)[0m
[32m[2025-04-24 11:34:30][INFO][UIAutomation] Clicked center at (1211, 344)[0m
[32m[2025-04-24 11:34:35][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-24 11:34:35][INFO][UIAutomation] Clicked at position (1322, 344)[0m
[32m[2025-04-24 11:34:35][INFO][UIAutomation] Clicked center at (1322, 344)[0m
[32m[2025-04-24 11:34:35][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 11:34:42][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 11:34:43][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 11:34:43][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 11:34:43][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:34:48][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 11:34:58][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 11:34:58][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 11:34:58][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 11:35:00][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 11:35:00][INFO][UIAutomation] Clicked at position (211, 195)[0m
[32m[2025-04-24 11:35:00][INFO][UIAutomation] Clicked top-left at (211, 195)[0m
[32m[2025-04-24 11:35:00][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:35:00][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:35:01][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:35:01][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:35:01][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:35:33][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:35:38][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:35:38][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:35:38][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:35:38][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 11:35:38][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:35:40][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 11:35:41][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 11:35:41][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 11:35:43][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 11:35:43][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 11:35:43][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 11:35:45][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 11:35:45][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 11:35:45][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 11:35:47][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 11:35:47][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 11:35:47][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 11:35:49][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 11:35:49][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 11:35:49][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 11:35:51][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 11:35:52][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 11:35:52][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 11:35:54][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 11:35:54][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 11:35:54][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 11:35:56][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 11:35:56][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 11:35:56][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 11:35:58][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 11:35:58][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 11:35:58][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 11:36:00][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 11:36:01][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 11:36:01][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 11:36:11][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 11:36:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:36:16][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 11:36:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:36:16][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 11:36:23][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 11:36:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:36:23][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:36:28][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 11:36:38][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-04-24 11:36:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:36:40][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 11:36:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:36:40][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:36:40][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:36:41][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:36:41][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:36:41][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:38:33][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:38:38][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:38:38][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:38:38][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:38:38][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:38:43][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 11:38:43][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 11:38:43][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 11:38:43][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:38:45][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 11:38:45][INFO][UIAutomation] Clicked at position (211, 366)[0m
[32m[2025-04-24 11:38:45][INFO][UIAutomation] Clicked top-left at (211, 366)[0m
[32m[2025-04-24 11:38:45][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:38:45][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:38:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:38:46][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:38:46][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:39:22][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:39:27][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:39:27][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:39:27][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:39:27][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:39:32][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 11:39:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:39:33][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:39:35][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 11:39:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:39:35][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:39:35][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:39:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:39:36][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:39:36][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:43:42][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:43:47][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:43:47][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:43:47][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:43:47][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:43:52][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 11:43:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:43:52][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:43:54][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 11:43:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 11:43:54][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:43:54][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:43:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:43:55][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:43:55][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 11:44:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 11:44:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 11:44:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 11:44:06][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 11:44:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 11:44:11][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 11:44:12][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 11:44:12][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 11:44:12][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:44:14][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 11:44:14][INFO][UIAutomation] Clicked at position (211, 366)[0m
[32m[2025-04-24 11:44:14][INFO][UIAutomation] Clicked top-left at (211, 366)[0m
[32m[2025-04-24 11:44:14][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 11:44:14][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 11:44:15][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 11:44:15][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 11:44:15][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 13:52:54][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 13:52:59][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 13:52:59][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 13:52:59][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 13:52:59][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 13:52:59][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 13:53:01][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 13:53:01][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:53:03][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 13:53:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:53:19][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 13:53:24][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 13:53:24][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 13:53:24][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 13:53:24][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 13:53:24][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 13:53:26][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 13:53:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:53:28][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 13:53:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:53:45][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 13:53:50][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 13:53:50][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 13:53:50][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 13:53:50][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 13:53:50][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 13:53:52][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 13:53:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:53:54][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 13:53:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:54:57][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 13:55:02][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 13:55:02][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 13:55:02][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 13:55:02][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 13:55:02][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 13:55:04][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 13:55:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:55:07][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 13:55:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:55:09][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 13:55:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:55:11][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 13:55:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:55:13][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 13:55:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 13:57:59][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 13:58:04][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 13:58:04][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 13:58:04][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 13:58:04][INFO][UIAutomation] F5进入蓝牙选项[0m
[33m[2025-04-24 13:58:04][WARN][UIAutomation] Task queue is empty or an error occurred: local variable 'pyautogui' referenced before assignment[0m
[32m[2025-04-24 13:58:04][INFO][UIAutomation] Started 1 worker threads[0m
[33m[2025-04-24 13:58:05][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:06][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:07][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:08][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:09][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:10][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:11][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:12][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:13][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:14][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:15][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:16][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:17][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:18][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:19][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:21][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:22][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:23][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:24][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:25][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:26][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:27][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:30][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:31][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:32][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:33][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:34][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:35][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:37][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 13:58:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:01:07][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:01:12][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:01:12][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:01:12][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:01:12][INFO][UIAutomation] F5进入蓝牙选项[0m
[33m[2025-04-24 14:01:12][WARN][UIAutomation] Task queue is empty or an error occurred: local variable 'pyautogui' referenced before assignment[0m
[32m[2025-04-24 14:01:12][INFO][UIAutomation] Started 1 worker threads[0m
[33m[2025-04-24 14:01:13][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:14][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:15][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:16][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:17][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:18][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:19][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:21][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:22][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:23][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:24][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:25][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:26][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:27][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:01:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:02:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:02:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:02:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:02:06][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:02:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:02:06][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:02:08][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:02:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:02:10][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:02:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:02:12][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:02:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:03:47][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:03:52][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:03:52][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:03:52][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:03:52][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:03:52][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:03:54][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:03:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:03:56][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:03:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:03:58][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:03:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:06:43][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:06:48][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:06:48][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:06:48][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:06:48][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:06:48][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:06:50][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:06:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:06:52][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:06:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:06:55][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:06:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:06:57][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:06:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:06:59][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:06:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:07:13][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:07:18][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:07:18][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:07:18][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:07:18][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:07:18][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:07:20][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 14:07:21][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 14:07:21][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 14:07:23][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 14:07:23][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 14:07:23][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 14:07:25][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 14:07:25][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 14:07:25][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 14:07:27][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 14:07:27][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 14:07:27][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 14:07:29][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 14:07:30][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 14:07:30][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 14:07:32][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 14:07:32][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 14:07:32][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 14:07:34][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 14:07:34][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 14:07:34][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 14:07:36][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 14:07:36][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 14:07:36][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 14:07:38][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 14:07:38][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 14:07:38][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 14:07:40][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 14:07:41][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 14:07:41][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 14:07:51][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-24 14:07:51][INFO][UIAutomation] Clicked at position (1211, 344)[0m
[32m[2025-04-24 14:07:51][INFO][UIAutomation] Clicked center at (1211, 344)[0m
[32m[2025-04-24 14:07:56][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-24 14:07:56][INFO][UIAutomation] Clicked at position (1322, 344)[0m
[32m[2025-04-24 14:07:56][INFO][UIAutomation] Clicked center at (1322, 344)[0m
[32m[2025-04-24 14:07:56][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 14:08:03][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 14:08:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:08:03][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:08:08][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:08:18][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-04-24 14:08:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:08:21][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 14:08:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:08:21][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:08:21][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:08:22][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:09:13][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:09:18][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:09:18][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:09:18][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:09:18][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:09:18][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:09:19][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:09:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:09:20][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:09:20][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:10:43][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:10:48][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:10:48][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:10:48][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:10:48][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:10:48][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:10:48][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:10:48][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:10:48][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:18:47][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:18:52][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:18:52][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:18:52][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:18:52][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:18:52][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:18:53][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:18:53][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:18:53][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:19:33][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:19:38][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:19:38][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:19:38][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:19:38][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:19:38][INFO][UIAutomation] F5进入蓝牙选项[0m
[33m[2025-04-24 14:19:38][WARN][UIAutomation] Task queue is empty or an error occurred: local variable 'pyautogui' referenced before assignment[0m
[33m[2025-04-24 14:19:39][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:41][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:42][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:43][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:44][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:50][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:52][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:56][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:57][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:58][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:19:59][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:20:00][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:20:01][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:20:02][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:20:03][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:20:04][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-04-24 14:20:05][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:20:17][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:20:22][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:20:22][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:20:22][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:20:22][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:20:22][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:20:24][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:20:24][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:20:24][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:20:40][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:20:45][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:20:45][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:20:45][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:20:45][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:20:45][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:20:45][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:20:45][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:20:45][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:21:34][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:21:39][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:21:39][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:21:39][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:21:39][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:21:39][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:21:41][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:21:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:21:43][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:21:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:21:43][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:21:43][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:21:43][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:21:53][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:21:58][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:21:58][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:21:58][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:21:58][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:21:58][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:22:00][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:22:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:22:02][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:22:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:22:02][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:22:02][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:22:02][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:23:42][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:23:47][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:23:47][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:23:47][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:23:47][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:23:47][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:23:49][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 14:23:50][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 14:23:50][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 14:23:52][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 14:23:52][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 14:23:52][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 14:23:52][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:23:52][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:23:52][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:24:39][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:24:44][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:24:44][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:24:44][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:24:44][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:24:44][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:24:46][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:24:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:24:48][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:24:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:24:51][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:24:51][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:24:53][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:24:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:24:55][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:24:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:24:57][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 14:24:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:24:59][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 14:24:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:25:01][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 14:25:01][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:25:18][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:25:23][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:25:23][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:25:23][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:25:23][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:25:23][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:25:26][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:25:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:25:28][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:25:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:25:30][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:25:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:25:32][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:25:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:25:34][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:25:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:25:36][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 14:25:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:25:38][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 14:25:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:07][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:31:12][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:31:12][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:31:12][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:31:12][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:31:12][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:31:15][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:31:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:17][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:31:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:19][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:31:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:21][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:31:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:23][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:31:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:25][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 14:31:25][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:27][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 14:31:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:29][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 14:31:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:31][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 14:31:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:31:33][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 14:31:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:17][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:32:22][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:32:22][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:32:22][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:32:22][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:32:22][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:32:24][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:32:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:26][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:32:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:28][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:32:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:30][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:32:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:32][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:32:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:35][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 14:32:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:37][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 14:32:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:39][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 14:32:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:41][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 14:32:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:43][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 14:32:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:53][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 14:32:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:58][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 14:32:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:32:58][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 14:33:05][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 14:33:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:33:06][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:33:11][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:33:21][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:33:21][INFO][UIAutomation] Clicked at position (1749, 174)[0m
[32m[2025-04-24 14:33:21][INFO][UIAutomation] Clicked center at (1749, 174)[0m
[32m[2025-04-24 14:33:23][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 14:33:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:33:23][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:33:23][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:33:24][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:33:24][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:33:24][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:36:33][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:36:38][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:36:38][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:36:38][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:36:38][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:36:38][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:36:40][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:36:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:36:42][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:36:42][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:36:44][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:36:44][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:36:46][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:36:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:36:48][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:36:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:36:51][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 14:36:51][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 14:36:51][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 14:36:53][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 14:36:53][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 14:36:53][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 14:36:55][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 14:36:55][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 14:36:55][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 14:36:57][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 14:36:57][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 14:36:57][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 14:36:59][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 14:37:00][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 14:37:00][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 14:37:10][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 14:37:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:37:15][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 14:37:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:37:15][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 14:37:22][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 14:37:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:37:22][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:37:27][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:37:37][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-04-24 14:37:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:37:39][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 14:37:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:37:39][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:37:39][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:37:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:37:40][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:37:40][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:39:13][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:39:18][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:39:18][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:39:18][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:39:18][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:39:18][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:39:20][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:39:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:39:22][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:39:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:39:24][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:39:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:39:26][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:39:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:39:29][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:39:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:39:31][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 14:39:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:39:33][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 14:39:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:40:57][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:41:02][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:41:02][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:41:02][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:41:02][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:41:02][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:41:04][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:41:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:06][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:41:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:08][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:41:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:10][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:41:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:12][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:41:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:14][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 14:41:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:16][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 14:41:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:19][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 14:41:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:21][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 14:41:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:41:23][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 14:41:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:42:09][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:42:14][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:42:14][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:42:14][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:42:14][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:42:14][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:42:16][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 14:42:16][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 14:42:16][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 14:42:18][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 14:42:18][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 14:42:18][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 14:42:20][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 14:42:20][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 14:42:20][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 14:42:22][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 14:42:23][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 14:42:23][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 14:42:25][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 14:42:25][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 14:42:25][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 14:42:27][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 14:42:27][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 14:42:27][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 14:42:29][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 14:42:29][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 14:42:29][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 14:42:31][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 14:42:31][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 14:42:31][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 14:42:33][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 14:42:34][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 14:42:34][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 14:42:36][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 14:42:36][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 14:42:36][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 14:42:46][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-24 14:42:46][INFO][UIAutomation] Clicked at position (1211, 344)[0m
[32m[2025-04-24 14:42:46][INFO][UIAutomation] Clicked center at (1211, 344)[0m
[32m[2025-04-24 14:42:51][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-24 14:42:51][INFO][UIAutomation] Clicked at position (1322, 344)[0m
[32m[2025-04-24 14:42:51][INFO][UIAutomation] Clicked center at (1322, 344)[0m
[32m[2025-04-24 14:42:51][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 14:42:58][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 14:42:59][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 14:42:59][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 14:42:59][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:43:04][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:43:14][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:43:14][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:43:14][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:43:16][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 14:43:16][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 14:43:16][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 14:43:16][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:43:16][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:43:17][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:43:17][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:43:17][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:44:48][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:44:53][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:44:53][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:44:53][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:44:53][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:44:53][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:44:55][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:44:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:44:57][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:44:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:44:59][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:44:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:01][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:45:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:31][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:45:36][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:45:36][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:45:36][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:45:36][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:45:36][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:45:38][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:45:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:40][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:45:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:42][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:45:42][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:44][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:45:44][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:46][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:45:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:48][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 14:45:49][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:51][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 14:45:51][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:53][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 14:45:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:55][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 14:45:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:45:57][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 14:45:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:46:11][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:46:16][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:46:16][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:46:16][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:46:16][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:46:16][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:46:18][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:46:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:46:20][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:46:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:46:22][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:46:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:47:23][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:47:28][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:47:28][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:47:28][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:47:28][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:47:28][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 14:47:30][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 14:47:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:47:32][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 14:47:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:47:34][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 14:47:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:47:36][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 14:47:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:47:38][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 14:47:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:47:40][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 14:47:41][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 14:47:41][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 14:47:43][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 14:47:43][INFO][UIAutomation] Clicked at position (1391, 781)[0m
[32m[2025-04-24 14:47:43][INFO][UIAutomation] Clicked center at (1391, 781)[0m
[32m[2025-04-24 14:47:45][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 14:47:45][INFO][UIAutomation] Clicked at position (1374, 813)[0m
[32m[2025-04-24 14:47:45][INFO][UIAutomation] Clicked center at (1374, 813)[0m
[32m[2025-04-24 14:47:47][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 14:47:47][INFO][UIAutomation] Clicked at position (1345, 947)[0m
[32m[2025-04-24 14:47:47][INFO][UIAutomation] Clicked center at (1345, 947)[0m
[32m[2025-04-24 14:47:49][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 14:47:49][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 14:47:49][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 14:47:59][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 14:48:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:48:05][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 14:48:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:48:05][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 14:48:12][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 14:48:12][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 14:48:12][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 14:48:12][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:48:17][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:48:27][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:48:27][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:48:27][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:48:29][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 14:48:30][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 14:48:30][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 14:48:30][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:48:30][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:48:31][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:48:31][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:48:31][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:49:24][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:49:29][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:49:29][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:49:29][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:49:29][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:49:34][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 14:49:34][INFO][UIAutomation] Clicked at position (210, 193)[0m
[32m[2025-04-24 14:49:34][INFO][UIAutomation] Clicked top-left at (210, 193)[0m
[32m[2025-04-24 14:49:34][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:49:39][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:49:49][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:49:49][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:49:49][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:49:51][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 14:49:54][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 14:49:54][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 14:49:54][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 14:49:54][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:49:54][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:49:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:49:55][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:49:55][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:50:18][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:50:23][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:50:23][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:50:23][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:50:23][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:50:28][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 14:50:28][INFO][UIAutomation] Clicked at position (210, 193)[0m
[32m[2025-04-24 14:50:28][INFO][UIAutomation] Clicked top-left at (210, 193)[0m
[32m[2025-04-24 14:50:28][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:50:33][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:50:43][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:50:43][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:50:43][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:50:53][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 14:50:55][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 14:50:55][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 14:50:55][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 14:50:55][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:50:55][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:50:56][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:50:56][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:50:56][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:53:14][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:53:19][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:53:19][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:53:19][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:53:19][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:53:24][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 14:53:24][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 14:53:24][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 14:53:24][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:53:29][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:53:39][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:53:39][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:53:39][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:53:49][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 14:53:51][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 14:53:52][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 14:53:52][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 14:53:52][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:53:52][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:53:52][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:53:52][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:54:37][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:54:42][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:54:42][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:54:42][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:54:42][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:54:47][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 14:54:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:54:47][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:54:52][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:55:02][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:55:02][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:55:02][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:55:12][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 14:55:14][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 14:55:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:55:14][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:55:14][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 14:55:14][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:55:14][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:55:40][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:55:45][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:55:45][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:55:45][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:55:45][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:55:50][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 14:55:50][INFO][UIAutomation] Clicked at position (210, 193)[0m
[32m[2025-04-24 14:55:50][INFO][UIAutomation] Clicked top-left at (210, 193)[0m
[32m[2025-04-24 14:55:50][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:55:55][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:56:05][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:56:05][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:56:05][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:56:07][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 14:56:07][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 14:56:07][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 14:56:07][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:56:07][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:56:08][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:56:08][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:56:08][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:57:39][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:57:44][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:57:44][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:57:44][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:57:44][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:57:54][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 14:57:59][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 14:57:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:57:59][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:58:04][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:58:15][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:58:15][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:58:15][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:58:17][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 14:58:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:58:17][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:58:17][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:58:18][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:58:18][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:58:18][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:58:46][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 14:58:51][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 14:58:51][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 14:58:51][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 14:58:51][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 14:59:01][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 14:59:06][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 14:59:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:59:07][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:59:12][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 14:59:22][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 14:59:22][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 14:59:22][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 14:59:24][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 14:59:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 14:59:24][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 14:59:24][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 14:59:25][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 14:59:25][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 14:59:25][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 14:59:56][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:00:01][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:00:01][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:00:01][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:00:01][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:00:11][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:00:17][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 15:00:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:00:17][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:00:22][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:00:32][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:00:32][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:00:32][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:00:34][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 15:00:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:00:34][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:00:34][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:00:34][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:00:34][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:01:30][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:01:35][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:01:35][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:01:35][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:01:35][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:01:45][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:01:55][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:01:55][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:01:55][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:01:55][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:02:00][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:02:10][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:02:10][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:02:10][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:02:20][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:02:22][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:02:23][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 15:02:23][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 15:02:23][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:02:23][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:02:24][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:02:24][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:02:24][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:02:55][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:03:00][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:03:00][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:03:00][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:03:00][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:03:10][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:03:20][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:03:20][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:03:20][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:03:20][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:03:25][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:03:35][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:03:36][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:03:36][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:03:46][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:03:51][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:03:51][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 15:03:51][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 15:03:51][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:03:51][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:03:51][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:03:51][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:04:46][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:04:51][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:04:51][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:04:51][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:04:51][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:05:01][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:05:11][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:05:12][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:05:12][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:05:12][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:05:17][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:05:27][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:05:27][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:05:27][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:05:37][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:05:52][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:05:52][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 15:05:52][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 15:05:52][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:05:52][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:05:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:05:53][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:05:53][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:06:33][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:06:38][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:06:38][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:06:38][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:06:38][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:06:48][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:06:58][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:06:58][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:06:58][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:06:58][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:07:03][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:07:14][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:07:14][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:07:14][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:07:29][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:07:31][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:07:31][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 15:07:31][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 15:07:31][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:07:31][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:07:31][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:07:31][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:09:18][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:09:23][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:09:23][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:09:23][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:09:23][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:09:33][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:09:43][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 15:09:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:09:43][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:09:48][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:09:58][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:09:59][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:09:59][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:10:09][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:10:11][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 15:10:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:10:11][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:10:11][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:10:11][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:10:11][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:10:34][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:10:39][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:10:39][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:10:39][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:10:39][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:10:49][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:11:04][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:11:04][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:11:04][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:11:04][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:11:09][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:11:19][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:11:19][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:11:19][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:11:29][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:11:31][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:11:32][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 15:11:32][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 15:11:32][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:11:32][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:11:32][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:11:32][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:12:45][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:12:50][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:12:50][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:12:50][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:12:50][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:12:50][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:12:52][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:12:53][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:12:53][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:12:55][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:12:55][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:12:55][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:12:57][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:12:57][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:12:57][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:12:59][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:12:59][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:12:59][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:13:01][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:13:01][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:13:01][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:13:03][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:13:04][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:13:04][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:13:06][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:13:06][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:13:06][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:13:08][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:13:08][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:13:08][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:13:10][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:13:10][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:13:10][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:13:12][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:13:13][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:13:13][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:13:23][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:13:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:13:28][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:13:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:13:28][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:13:40][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:13:55][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:13:55][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:13:55][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:13:55][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:14:00][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:14:10][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:14:11][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:14:11][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:14:21][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:14:23][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:14:23][INFO][UIAutomation] Clicked at position (211, 190)[0m
[32m[2025-04-24 15:14:23][INFO][UIAutomation] Clicked top-left at (211, 190)[0m
[32m[2025-04-24 15:14:23][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:14:23][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:14:24][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:14:24][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:14:24][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:18:15][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:18:20][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:18:20][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:18:20][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:18:20][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:18:20][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:18:22][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 15:18:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:18:24][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 15:18:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:18:26][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 15:18:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:18:28][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 15:18:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:18:40][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:18:45][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:18:45][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:18:45][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:18:45][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:18:45][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:18:47][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:18:47][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:18:47][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:18:49][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:18:49][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:18:49][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:18:51][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:18:52][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:18:52][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:18:54][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:18:54][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:18:54][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:18:56][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:18:56][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:18:56][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:18:58][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:18:58][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:18:58][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:19:00][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:19:01][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:19:01][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:19:03][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:19:03][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:19:03][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:19:05][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:19:05][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:19:05][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:19:07][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:19:07][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:19:07][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:19:17][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:19:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:19:22][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:19:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:19:22][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:19:35][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:19:50][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 15:19:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:19:50][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:19:55][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:20:05][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-04-24 15:20:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:22:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:22:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:22:06][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:22:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:22:06][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:22:08][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 15:22:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:10][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 15:22:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:12][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 15:22:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:14][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 15:22:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:16][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 15:22:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:18][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 15:22:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:21][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 15:22:21][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:23][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-24 15:22:23][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:25][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-24 15:22:25][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:27][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-24 15:22:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:22:37][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:22:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:23:00][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:23:05][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:23:05][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:23:05][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:23:05][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:23:05][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:23:07][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 15:23:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:23:09][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 15:23:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:23:11][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 15:23:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:24:20][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:24:25][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:24:25][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:24:25][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:24:25][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:24:27][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:24:29][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 15:24:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:24:32][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 15:24:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:24:34][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 15:24:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:24:36][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:24:36][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:24:36][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:24:38][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:24:38][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:24:38][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:24:40][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:24:40][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:24:40][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:24:42][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:24:43][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:24:43][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:24:45][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:24:45][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:24:45][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:24:47][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:24:47][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:24:47][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:24:49][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:24:49][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:24:49][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:24:59][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:24:59][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:25:04][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:25:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:25:04][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:25:17][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:25:32][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-04-24 15:25:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:25:32][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:25:37][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:25:51][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:25:56][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:25:56][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:25:56][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:25:56][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:25:58][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:26:00][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-04-24 15:26:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:26:02][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-04-24 15:26:02][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:26:04][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-24 15:26:04][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:26:06][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-24 15:26:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:26:09][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-24 15:26:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:26:11][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-24 15:26:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:26:13][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-04-24 15:26:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:26:30][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:26:35][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:26:35][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:26:35][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:26:35][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:26:37][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:26:39][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:26:40][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:26:40][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:26:42][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:26:42][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:26:42][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:26:44][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:26:44][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:26:44][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:26:46][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:26:46][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:26:46][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:26:48][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:26:48][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:26:48][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:26:50][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:26:51][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:26:51][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:26:53][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:26:53][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:26:53][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:26:55][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:26:55][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:26:55][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:26:57][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:26:57][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:26:57][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:26:59][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:27:00][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:27:00][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:27:10][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:27:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:27:15][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:27:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:27:15][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:27:27][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:27:42][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:27:42][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:27:42][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:27:42][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:27:47][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:27:57][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:27:58][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:27:58][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:28:08][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:28:10][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 15:28:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:28:10][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:28:10][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:28:10][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:28:10][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:30:57][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:31:02][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:31:02][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:31:02][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:31:02][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:31:04][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:31:06][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:31:06][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:31:06][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:31:08][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:31:08][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:31:08][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:31:10][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:31:11][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:31:11][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:31:13][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:31:13][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:31:13][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:31:15][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:31:15][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:31:15][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:31:17][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:31:17][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:31:17][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:31:19][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:31:20][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:31:20][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:31:22][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:31:22][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:31:22][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:31:24][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:31:24][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:31:24][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:31:26][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:31:26][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:31:26][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:31:36][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:31:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:31:41][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:31:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:31:41][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:31:49][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:31:54][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:31:54][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:31:54][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:31:54][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:31:59][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:32:09][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:32:09][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:32:09][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:32:19][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:32:21][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 15:32:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:32:22][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:32:22][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:32:23][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:32:23][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:32:23][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:33:29][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:33:34][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:33:34][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:33:34][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:33:34][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:33:36][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:33:38][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:33:38][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:33:38][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:33:40][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:33:40][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:33:40][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:33:42][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:33:43][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:33:43][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:33:45][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:33:45][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:33:45][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:33:47][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:33:47][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:33:47][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:33:49][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:33:49][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:33:49][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:33:51][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:33:52][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:33:52][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:33:54][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:33:54][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:33:54][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:33:56][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:33:56][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:33:56][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:33:58][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:33:58][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:33:58][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:34:08][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:34:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:34:13][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:34:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:34:13][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:34:21][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:34:26][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:34:26][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:34:26][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:34:26][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:34:31][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:34:41][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:34:41][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:34:41][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:34:51][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:34:53][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 15:34:53][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:34:53][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:34:53][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:34:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:34:54][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:34:54][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:35:33][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:35:38][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:35:38][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:35:38][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:35:38][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:35:40][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:35:42][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:35:43][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:35:43][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:35:45][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:35:45][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:35:45][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:35:47][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:35:47][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:35:47][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:35:49][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:35:49][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:35:49][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:35:51][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:35:51][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:35:51][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:35:53][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:35:54][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:35:54][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:35:56][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:35:56][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:35:56][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:35:58][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:35:58][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:35:58][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:36:00][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:36:00][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:36:00][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:36:02][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:36:03][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:36:03][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:36:13][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:36:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:36:18][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:36:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:36:18][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:36:25][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:36:30][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:36:30][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:36:30][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:36:30][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:36:35][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:36:45][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:36:46][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:36:46][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:36:56][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:36:58][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 15:36:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:36:58][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:36:58][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:36:58][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:36:58][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:37:30][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:37:35][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:37:35][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:37:35][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:37:35][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:37:37][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:37:39][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:37:39][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:37:39][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:37:41][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:37:41][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:37:41][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:37:43][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:37:43][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:37:43][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:37:45][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:37:45][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:37:45][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:37:47][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:37:48][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:37:48][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:37:50][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:37:50][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:37:50][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:37:52][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:37:52][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:37:52][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:37:54][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:37:54][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:37:54][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:37:56][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:37:57][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:37:57][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:37:59][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:37:59][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:37:59][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:38:09][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:38:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:38:14][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:38:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:38:14][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:38:21][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:38:26][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:38:27][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:38:27][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:38:27][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:38:32][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:38:37][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:38:37][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:38:37][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:38:42][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:38:44][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-04-24 15:38:44][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:38:44][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:38:44][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:38:44][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:38:44][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:39:16][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:39:21][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:39:21][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:39:21][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:39:21][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:39:23][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:39:25][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:39:25][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:39:25][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:39:27][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:39:27][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:39:27][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:39:29][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:39:29][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:39:29][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:39:31][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:39:31][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:39:31][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:39:33][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:39:34][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:39:34][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:39:36][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:39:36][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:39:36][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:39:38][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:39:38][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:39:38][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:39:40][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:39:40][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:39:40][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:39:42][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:39:43][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:39:43][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:39:45][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:39:45][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:39:45][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:39:55][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:39:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:40:00][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:40:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:40:00][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:40:07][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:40:12][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:40:13][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:40:13][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:40:13][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:40:18][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:40:28][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:40:28][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:40:28][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:40:33][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:40:35][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:40:35][INFO][UIAutomation] Clicked at position (139, 192)[0m
[32m[2025-04-24 15:40:35][INFO][UIAutomation] Clicked top-left at (139, 192)[0m
[32m[2025-04-24 15:40:35][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:40:35][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-24 15:40:35][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:40:35][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:44:51][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:44:56][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:44:56][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:44:56][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:44:56][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:44:58][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:45:00][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:45:00][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:45:00][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:45:02][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:45:02][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:45:02][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:45:04][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:45:05][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:45:05][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:45:07][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:45:07][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:45:07][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:45:09][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:45:09][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:45:09][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:45:11][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:45:11][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:45:11][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:45:13][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:45:13][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:45:13][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:45:15][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:45:16][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:45:16][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:45:18][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:45:18][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:45:18][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:45:20][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:45:20][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:45:20][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:45:30][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:45:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:45:35][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:45:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:45:35][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:45:43][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:45:48][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:45:48][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:45:48][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:45:48][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:45:53][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:46:03][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:46:03][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:46:03][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:46:08][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:46:10][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:46:10][INFO][UIAutomation] Clicked at position (139, 192)[0m
[32m[2025-04-24 15:46:10][INFO][UIAutomation] Clicked top-left at (139, 192)[0m
[32m[2025-04-24 15:46:10][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:46:10][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:46:11][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:46:11][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:46:11][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:48:19][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:48:24][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:48:24][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:48:24][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:48:24][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:48:26][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:48:28][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:48:28][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:48:28][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:48:30][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:48:30][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:48:30][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:48:32][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:48:33][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:48:33][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:48:35][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:48:35][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:48:35][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:48:37][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:48:37][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:48:37][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:48:39][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:48:39][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:48:39][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:48:41][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:48:41][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:48:41][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:48:43][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:48:44][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:48:44][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:48:46][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:48:46][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:48:46][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:48:48][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:48:48][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:48:48][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:48:58][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:48:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:49:03][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:49:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:49:03][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:49:10][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:49:16][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:49:16][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:49:16][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:49:16][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:49:21][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:49:31][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:49:31][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:49:31][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:49:36][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:49:38][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:49:38][INFO][UIAutomation] Clicked at position (139, 192)[0m
[32m[2025-04-24 15:49:38][INFO][UIAutomation] Clicked top-left at (139, 192)[0m
[32m[2025-04-24 15:49:38][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:49:38][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:49:39][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:49:39][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:49:39][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:49:54][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:49:59][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:49:59][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:49:59][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:49:59][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:50:01][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:50:04][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:50:04][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:50:04][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:50:06][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:50:06][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:50:06][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:50:08][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:50:08][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:50:08][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:50:10][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:50:10][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:50:10][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:50:12][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:50:13][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:50:13][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:50:15][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:50:15][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:50:15][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:50:17][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:50:17][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:50:17][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:50:19][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:50:19][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:50:19][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:50:21][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:50:22][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:50:22][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:50:24][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:50:24][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:50:24][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:50:34][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:50:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:50:39][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:50:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:50:39][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:50:46][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:50:51][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:50:52][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:50:52][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:50:52][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:50:57][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:51:07][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:51:07][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:51:07][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:51:12][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:51:14][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:51:14][INFO][UIAutomation] Clicked at position (139, 192)[0m
[32m[2025-04-24 15:51:14][INFO][UIAutomation] Clicked top-left at (139, 192)[0m
[32m[2025-04-24 15:51:14][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:51:14][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:51:15][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:51:15][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:51:15][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-24 15:52:03][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-24 15:52:08][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-24 15:52:08][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-24 15:52:08][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-24 15:52:08][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-24 15:52:10][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-24 15:52:12][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-24 15:52:13][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-24 15:52:13][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-24 15:52:15][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-24 15:52:15][INFO][UIAutomation] Clicked at position (710, 300)[0m
[32m[2025-04-24 15:52:15][INFO][UIAutomation] Clicked center at (710, 300)[0m
[32m[2025-04-24 15:52:17][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-24 15:52:17][INFO][UIAutomation] Clicked at position (782, 383)[0m
[32m[2025-04-24 15:52:17][INFO][UIAutomation] Clicked center at (782, 383)[0m
[32m[2025-04-24 15:52:19][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-24 15:52:19][INFO][UIAutomation] Clicked at position (709, 322)[0m
[32m[2025-04-24 15:52:19][INFO][UIAutomation] Clicked center at (709, 322)[0m
[32m[2025-04-24 15:52:21][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-24 15:52:21][INFO][UIAutomation] Clicked at position (941, 483)[0m
[32m[2025-04-24 15:52:21][INFO][UIAutomation] Clicked center at (941, 483)[0m
[32m[2025-04-24 15:52:23][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-24 15:52:24][INFO][UIAutomation] Clicked at position (1243, 324)[0m
[32m[2025-04-24 15:52:24][INFO][UIAutomation] Clicked center at (1243, 324)[0m
[32m[2025-04-24 15:52:26][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-24 15:52:26][INFO][UIAutomation] Clicked at position (1420, 686)[0m
[32m[2025-04-24 15:52:26][INFO][UIAutomation] Clicked center at (1420, 686)[0m
[32m[2025-04-24 15:52:28][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-24 15:52:28][INFO][UIAutomation] Clicked at position (1403, 718)[0m
[32m[2025-04-24 15:52:28][INFO][UIAutomation] Clicked center at (1403, 718)[0m
[32m[2025-04-24 15:52:30][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-24 15:52:30][INFO][UIAutomation] Clicked at position (1374, 852)[0m
[32m[2025-04-24 15:52:30][INFO][UIAutomation] Clicked center at (1374, 852)[0m
[32m[2025-04-24 15:52:32][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-24 15:52:32][INFO][UIAutomation] Clicked at position (695, 342)[0m
[32m[2025-04-24 15:52:32][INFO][UIAutomation] Clicked center at (695, 342)[0m
[32m[2025-04-24 15:52:42][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-24 15:52:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:52:48][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-24 15:52:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-24 15:52:48][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-24 15:52:55][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:53:00][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-04-24 15:53:00][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-24 15:53:00][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-24 15:53:00][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:53:05][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-04-24 15:53:15][INFO][UIAutomation] 关闭弹窗[0m
[32m[2025-04-24 15:53:15][INFO][UIAutomation] Clicked at position (1627, 255)[0m
[32m[2025-04-24 15:53:15][INFO][UIAutomation] Clicked center at (1627, 255)[0m
[32m[2025-04-24 15:53:20][INFO][UIAutomation] 向上滑动[0m
[32m[2025-04-24 15:53:23][INFO][UIAutomation] 单击取消勾选用例[0m
[32m[2025-04-24 15:53:23][INFO][UIAutomation] Clicked at position (139, 192)[0m
[32m[2025-04-24 15:53:23][INFO][UIAutomation] Clicked top-left at (139, 192)[0m
[32m[2025-04-24 15:53:23][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-24 15:53:23][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-24 15:53:24][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-24 15:53:24][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-24 15:53:24][INFO][UIAutomation] UI automation framework finished successfully[0m
