import cv2
import numpy as np
import pyautogui
import time
import os
import matplotlib.pyplot as plt
from datetime import datetime


class VisualStabilityDetector:
    """
    视觉稳定性检测器 - 与日志目录集成版

    使用说明:
    1. 创建检测器对象: detector = VisualStabilityDetector(log_screendir="D:/your/log/dir")
    2. 启动检测: result = detector.detect_completion()
    3. 检查结果: if result: print("任务完成")

    可选配置:
    - 设置监控区域: monitor_region=(x, y, width, height)
    - 调整相似度阈值: stability_threshold=0.98 (0.95-0.99)
    - 设置最小稳定时间: min_stable_time=10 (秒)
    - 设置检查间隔: check_interval=1 (秒)
    - 设置最大等待时间: max_wait_time=300 (秒)
    - 设置截图保存目录: log_screendir="你的日志目录"
    """

    def __init__(self, log_screendir=None, monitor_region=None, stability_threshold=0.98,
                 min_stable_time=10, check_interval=1, max_wait_time=300):
        """
        初始化视觉稳定性检测器

        :param log_screendir: 日志目录，截图将保存在此目录下的screenshots子目录
        :param monitor_region: 监控区域 (x, y, width, height)，None表示全屏
        :param stability_threshold: 稳定性阈值 (0-1, 值越高要求越严格)
        :param min_stable_time: 最小稳定时间(秒)
        :param check_interval: 检查间隔(秒)
        :param max_wait_time: 最大等待时间(秒)
        """
        self.monitor_region = monitor_region
        self.stability_threshold = stability_threshold
        self.min_stable_time = min_stable_time
        self.check_interval = check_interval
        self.max_wait_time = max_wait_time

        # 设置截图保存目录
        if log_screendir:
            # 在日志目录下创建screenshots子目录
            self.output_dir = os.path.join(log_screendir, "screenshots")
        else:
            # 默认截图目录
            self.output_dir = "screenshots"

        # 确保截图目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"截图将保存在: {os.path.abspath(self.output_dir)}")

        # 内部状态变量
        self.stable_start_time = None
        self.last_change_time = None
        self.previous_image = None
        self.stability_history = []
        self.timestamps = []

    def capture_screen(self):
        """捕获屏幕截图"""
        if self.monitor_region:
            return pyautogui.screenshot(region=self.monitor_region)
        return pyautogui.screenshot()

    def calculate_similarity(self, img1, img2):
        """
        计算两个图像的相似度（兼容OpenCV版本）

        :return: 相似度分数 (0-1, 1表示完全相同)
        """
        # 将PIL图像转换为numpy数组
        img1_np = np.array(img1)
        img2_np = np.array(img2)

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1_np, cv2.COLOR_RGB2GRAY)
        gray2 = cv2.cvtColor(img2_np, cv2.COLOR_RGB2GRAY)

        # 确保图像大小相同
        if gray1.shape != gray2.shape:
            gray2 = cv2.resize(gray2, (gray1.shape[1], gray1.shape[0]))

        # 方法1: 尝试使用OpenCV的SSIM（不同版本可能有不同实现）
        try:
            # OpenCV 4.x版本
            if hasattr(cv2, 'quality_QualitySSIM'):
                ssim = cv2.quality.QualitySSIM_create()
                score = ssim.compute(gray1, gray2)[0]
                return score[0] if isinstance(score, np.ndarray) else score
        except:
            pass

        # 方法2: 如果SSIM不可用，使用像素差异法
        # 计算绝对差异
        diff = cv2.absdiff(gray1, gray2)
        # 计算非零像素数量
        non_zero_count = np.count_nonzero(diff)
        # 计算总像素数
        total_pixels = diff.size
        # 计算变化比例
        change_ratio = non_zero_count / total_pixels
        # 返回相似度 (1 - 变化比例)
        return 1.0 - change_ratio

    def save_screenshot(self, image, prefix="change"):
        """
        保存截图到文件

        :param image: 要保存的图片
        :param prefix: 文件名前缀
        :return: 保存的文件路径
        """
        # 确保目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 生成时间戳文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        filename = os.path.join(self.output_dir, f"{prefix}_{timestamp}.png")

        # 保存图片
        image.save(filename)

        # 打印保存位置
        print(f"截图保存至: {os.path.abspath(filename)}")

        return filename

    def detect_completion(self):
        """
        检测任务是否完成（页面长时间无变化）

        :return: True - 任务完成, False - 任务未完成
        """
        start_time = time.time()
        self.last_change_time = start_time
        self.stable_start_time = None
        self.stability_history = []
        self.timestamps = []

        print(f"开始视觉稳定性检测...")
        print(f"监控区域: {self.monitor_region if self.monitor_region else '全屏'}")
        print(f"相似度阈值: {self.stability_threshold}, 最小稳定时间: {self.min_stable_time}秒")
        print(f"最大等待时间: {self.max_wait_time}秒")
        print(f"截图保存目录: {os.path.abspath(self.output_dir)}")

        # 初始截图
        self.previous_image = self.capture_screen()
        self.save_screenshot(self.previous_image, "initial")

        while time.time() - start_time < self.max_wait_time:
            try:
                # 捕获当前屏幕
                current_image = self.capture_screen()

                # 计算相似度
                similarity = self.calculate_similarity(self.previous_image, current_image)
                current_time = time.time()
                elapsed_time = current_time - start_time

                # 记录历史数据
                self.stability_history.append(similarity)
                self.timestamps.append(elapsed_time)

                print(f"[{elapsed_time:.1f}秒] 相似度: {similarity:.4f}", end='')

                if similarity < self.stability_threshold:
                    # 检测到变化
                    self.last_change_time = current_time
                    self.stable_start_time = None
                    print(" - 检测到变化")

                    # 保存变化截图
                    self.save_screenshot(current_image, "change")
                else:
                    # 没有显著变化
                    if self.stable_start_time is None:
                        self.stable_start_time = current_time
                        print(" - 开始稳定")
                    else:
                        stable_duration = current_time - self.stable_start_time
                        print(f" - 稳定中: {stable_duration:.1f}/{self.min_stable_time}秒")

                        # 检查是否达到最小稳定时间
                        if stable_duration >= self.min_stable_time:
                            print(f"\n✅ 任务完成! 页面稳定持续 {stable_duration:.1f}秒")

                            # 保存最终截图
                            self.save_screenshot(current_image, "stable_final")
                            return True

                # 更新上一张图像
                self.previous_image = current_image

                # 等待下一次检查
                time.sleep(self.check_interval)

            except Exception as e:
                print(f"检测过程中出错: {str(e)}")
                # 出错后继续尝试
                time.sleep(2)

        print(f"\n⏰ 等待超时 ({self.max_wait_time}秒)，任务可能未完成")
        return False


# 使用示例
def example_usage():
    """使用示例"""
    print("视觉稳定性检测器使用示例")
    # 创建检测器并指定日志目录
    from script.Framework.framework import log_dir
    # 创建检测器对象 (使用默认参数)
    detector = VisualStabilityDetector(
        monitor_region=None,  # 监控屏幕的一部分区域
        stability_threshold=0.98,  # 相似度阈值 (98%)
        min_stable_time=2,  # 需要稳定15秒
        max_wait_time=60,   # 最多等待5分钟
        # output_dir="D:/python_code/pc_ui_auto_svncode/script/logs/screenshot"  # 默认保存目录LOG_DIR
        log_screendir=log_dir
    )

    # 启动检测
    if detector.detect_completion():
        print("任务已完成！")
        # 保存最终截图到自定义路径
        final_image = pyautogui.screenshot()
        detector.save_screenshot(
            final_image,
            prefix="final_result"
        )
    else:
        print("任务未在指定时间内完成")
        # 这里可以添加超时处理逻辑
