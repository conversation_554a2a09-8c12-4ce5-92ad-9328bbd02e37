your_project/
├── act/
│   └── main.py
├── application_interface/
│   ├── mouse_actions.py
│   ├── keyboard_actions.py
│   └── exception_handler.py
├── framework/
│   └── framework.py
└── logs/
    └── automation_log.log



#pip show pyautogui Pillow 显示库文件路径
#包含库PIL ,pyscreeze
#PyAutoGUI中的confidence参数（如locateOnScreen、click）依赖OpenCV（opencv-python）才能生效
#pip install opencv-python
#confidence=0.9 就是告诉电脑：“只有当相似度达到 90% 时，才认为找到了目标”。
值范围：0.0（完全不匹配）到 1.0（完全匹配）
#pip install opencv-python -i https://pypi.tuna.tsinghua.edu.cn/simple
#pip install colorlog

