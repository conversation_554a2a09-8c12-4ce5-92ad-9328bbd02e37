import os
import json


class ImageResourceManager:
    """图片资源管理器"""

    def __init__(self):
        # 默认资源目录（不用改）
        self._resource_dir = r"D:/linyu/autotest/Testcode/resource/picture/"
        # 配置文件路径（放在同一目录）
        self._config_file = os.path.join(os.path.dirname(__file__), "picture_resource.json")
        self._resources = {}
        self.load_config()

    def load_config(self):
        try:
            with open(self._config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 清空当前资源
            self._resources.clear()

            # 构建资源路径映射（跳过注释字段）
            for name, filename in config.items():
                # 跳过以下划线开头的字段（视为注释）
                if name.startswith('_'):
                    continue
                self._resources[name] = os.path.join(self._resource_dir, filename)

            print(f"✅ 成功加载 {len(self._resources)} 个图片资源")
            print(f"✅ 图片资源路径 {(self._resource_dir)} ")
        except Exception as e:
            print(f"⚠️ 配置文件加载失败: {str(e)}")

    def set_resource_dir(self, new_path):
        """设置新的资源目录（可选）"""
        if os.path.exists(new_path):
            self._resource_dir = new_path
            self.load_config()  # 重新加载配置
            print(f"📁 资源目录已更新为: {new_path}")
        else:
            print(f"❌ 目录不存在: {new_path}")

    def get(self, name):
        """获取资源路径（最常用）"""
        path = self._resources.get(name)
        if not path:
            print(f"⚠️ 找不到资源: {name}")
            print("👉 请检查 image_config.json 是否包含该资源")
        return path

    def __getattr__(self, name):
        """直接通过 . 访问资源（更方便）"""
        return self.get(name)


# 创建全局实例（整个项目共用这个）
image_resources = ImageResourceManager()


# 兼容旧代码的类（如果原来用了 ImageResources.get() 可以继续用）
class ImageResources:
    @classmethod
    def get(cls, name):
        return image_resources.get(name)