import sys
import os
import time
from pathlib import Path
# 获取当前文件所在目录（act目录）
current_dir = Path(__file__).parent
# 获取项目根目录（script目录的父目录）
project_root = current_dir.parent.parent
# 将项目根目录添加到Python路径
sys.path.insert(0, str(project_root))
print(f"Project root: {project_root}")
print(f"Current directory: {os.getcwd()}")
print(f"Python path: {sys.path}")


from script.Framework.framework import UIAutomationFramework
from script.application_interface.mouse_actions import test_task1
from script.application_interface.ap_mouse_actions import ap_task

if __name__ == "__main__":
    # 创建自动化测试框架实15
    automation = UIAutomationFramework()
    # 添加测试任务
    time.sleep(5)
    # automation.add_task(ap_task, automation)#AP用例执行
    automation.add_task(test_task1, automation)#ACqua用例执行

    # 运行框架
    automation.run()

    print("All tasks completed")