[32m[2025-05-22 20:08:32][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 20:08:37][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 20:08:37][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 20:08:37][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 20:08:37][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 20:08:39][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 20:08:41][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 20:08:41][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 20:08:41][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 20:08:43][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 20:08:43][INFO][UIAutomation] Clicked at position (411, 301)[0m
[32m[2025-05-22 20:08:43][INFO][UIAutomation] Clicked center at (411, 301)[0m
[32m[2025-05-22 20:08:45][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-05-22 20:08:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:08:48][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 20:08:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:08:50][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-22 20:08:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:08:52][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-05-22 20:08:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:08:54][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 20:08:54][INFO][UIAutomation] Clicked at position (1132, 422)[0m
[32m[2025-05-22 20:08:54][INFO][UIAutomation] Clicked center at (1132, 422)[0m
[32m[2025-05-22 20:08:56][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-05-22 20:08:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:08:58][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 20:08:58][INFO][UIAutomation] Clicked at position (692, 641)[0m
[32m[2025-05-22 20:08:58][INFO][UIAutomation] Clicked center at (692, 641)[0m
[32m[2025-05-22 20:09:00][INFO][UIAutomation] 单击Connect[0m
[31m[2025-05-22 20:09:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:12:28][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 20:12:33][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 20:12:33][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 20:12:33][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 20:12:33][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 20:12:35][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 20:12:37][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 20:12:37][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 20:12:37][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 20:12:39][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 20:12:39][INFO][UIAutomation] Clicked at position (411, 301)[0m
[32m[2025-05-22 20:12:39][INFO][UIAutomation] Clicked center at (411, 301)[0m
[32m[2025-05-22 20:12:41][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 20:12:41][INFO][UIAutomation] Clicked at position (717, 386)[0m
[32m[2025-05-22 20:12:41][INFO][UIAutomation] Clicked center at (717, 386)[0m
[32m[2025-05-22 20:12:43][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 20:12:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:12:45][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-22 20:12:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:12:48][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-05-22 20:12:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:12:50][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 20:12:50][INFO][UIAutomation] Clicked at position (1132, 422)[0m
[32m[2025-05-22 20:12:50][INFO][UIAutomation] Clicked center at (1132, 422)[0m
[32m[2025-05-22 20:12:52][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-05-22 20:12:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:12:54][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 20:12:54][INFO][UIAutomation] Clicked at position (692, 641)[0m
[32m[2025-05-22 20:12:54][INFO][UIAutomation] Clicked center at (692, 641)[0m
[32m[2025-05-22 20:12:56][INFO][UIAutomation] 单击Connect[0m
[31m[2025-05-22 20:12:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:13:06][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-22 20:13:06][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:13:11][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-22 20:13:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:13:11][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 20:13:19][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-22 20:13:24][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-05-22 20:13:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:13:24][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 20:13:29][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-22 20:13:39][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-05-22 20:13:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:13:44][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-22 20:13:46][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-05-22 20:13:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:13:46][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 20:13:46][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-05-22 20:18:12][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 20:18:17][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 20:18:17][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 20:18:17][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 20:18:17][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 20:18:19][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 20:18:21][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 20:18:21][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 20:18:21][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 20:18:23][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 20:18:24][INFO][UIAutomation] Clicked at position (411, 301)[0m
[32m[2025-05-22 20:18:24][INFO][UIAutomation] Clicked center at (411, 301)[0m
[32m[2025-05-22 20:18:26][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 20:18:26][INFO][UIAutomation] Clicked at position (717, 386)[0m
[32m[2025-05-22 20:18:26][INFO][UIAutomation] Clicked center at (717, 386)[0m
[32m[2025-05-22 20:18:26][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 20:18:30][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 20:18:30][INFO][UIAutomation] Clicked at position (410, 323)[0m
[32m[2025-05-22 20:18:30][INFO][UIAutomation] Clicked center at (410, 323)[0m
[32m[2025-05-22 20:18:32][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 20:18:32][INFO][UIAutomation] Clicked at position (642, 484)[0m
[32m[2025-05-22 20:18:32][INFO][UIAutomation] Clicked center at (642, 484)[0m
[32m[2025-05-22 20:18:34][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 20:18:35][INFO][UIAutomation] Clicked at position (963, 325)[0m
[32m[2025-05-22 20:18:35][INFO][UIAutomation] Clicked center at (963, 325)[0m
[32m[2025-05-22 20:18:37][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 20:18:37][INFO][UIAutomation] Clicked at position (1140, 687)[0m
[32m[2025-05-22 20:18:37][INFO][UIAutomation] Clicked center at (1140, 687)[0m
[32m[2025-05-22 20:18:39][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 20:18:39][INFO][UIAutomation] Clicked at position (1123, 719)[0m
[32m[2025-05-22 20:18:39][INFO][UIAutomation] Clicked center at (1123, 719)[0m
[32m[2025-05-22 20:18:41][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 20:18:41][INFO][UIAutomation] Clicked at position (1094, 853)[0m
[32m[2025-05-22 20:18:41][INFO][UIAutomation] Clicked center at (1094, 853)[0m
[32m[2025-05-22 20:18:43][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 20:18:44][INFO][UIAutomation] Clicked at position (396, 343)[0m
[32m[2025-05-22 20:18:44][INFO][UIAutomation] Clicked center at (396, 343)[0m
[32m[2025-05-22 20:18:54][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 20:18:54][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 20:18:54][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 20:18:59][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 20:18:59][INFO][UIAutomation] Clicked at position (1042, 345)[0m
[32m[2025-05-22 20:18:59][INFO][UIAutomation] Clicked center at (1042, 345)[0m
[32m[2025-05-22 20:18:59][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 20:19:06][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-22 20:19:11][INFO][UIAutomation] 单击勾选用例[0m
[31m[2025-05-22 20:19:11][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:19:11][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 20:19:16][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-22 20:19:26][INFO][UIAutomation] 关闭弹窗[0m
[31m[2025-05-22 20:19:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:19:32][INFO][UIAutomation] 向上滑动[0m
[32m[2025-05-22 20:19:34][INFO][UIAutomation] 单击取消勾选用例[0m
[31m[2025-05-22 20:19:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:19:34][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 20:19:34][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-05-22 20:46:23][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 20:46:28][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 20:46:28][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 20:46:28][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 20:46:28][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 20:46:38][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 20:46:38][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 20:46:38][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 20:46:42][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 20:46:42][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 20:46:42][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 20:46:47][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 20:46:47][INFO][UIAutomation] Clicked at position (1042, 345)[0m
[32m[2025-05-22 20:46:47][INFO][UIAutomation] Clicked center at (1042, 345)[0m
[32m[2025-05-22 20:46:47][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 20:46:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 20:46:48][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 20:46:48][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 20:49:27][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 20:49:32][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 20:49:32][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 20:49:32][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 20:49:32][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 20:49:42][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 20:49:42][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 20:49:42][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 20:49:46][INFO][UIAutomation] 选择音量8/15[0m
[31m[2025-05-22 20:49:46][ERRO][UIAutomation] Image recognition failed: 'Box' object has no attribute 'right'[0m
[31m[2025-05-22 20:49:51][ERRO][UIAutomation] Image recognition failed: 'Box' object has no attribute 'right'[0m
[32m[2025-05-22 20:49:51][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 20:49:56][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 20:49:56][INFO][UIAutomation] Clicked at position (1042, 345)[0m
[32m[2025-05-22 20:49:56][INFO][UIAutomation] Clicked center at (1042, 345)[0m
[32m[2025-05-22 20:49:56][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 20:49:57][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 20:49:57][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 20:49:57][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 20:51:25][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 20:51:30][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 20:51:30][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 20:51:30][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 20:51:30][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 20:51:40][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 20:51:40][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 20:51:40][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 20:51:44][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 20:51:44][INFO][UIAutomation] Clicked at position (679, 495)[0m
[32m[2025-05-22 20:51:44][INFO][UIAutomation] Clicked top-right at (679, 495)[0m
[32m[2025-05-22 20:51:48][INFO][UIAutomation] Clicked at position (687, 492)[0m
[32m[2025-05-22 20:51:48][INFO][UIAutomation] Clicked top-right at (687, 492)[0m
[32m[2025-05-22 20:51:49][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 20:51:54][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 20:51:54][INFO][UIAutomation] Clicked at position (1042, 345)[0m
[32m[2025-05-22 20:51:54][INFO][UIAutomation] Clicked center at (1042, 345)[0m
[32m[2025-05-22 20:51:54][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-05-22 20:51:54][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 20:51:54][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 20:54:29][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 20:54:34][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 20:54:34][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 20:54:34][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 20:54:34][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 20:54:44][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 20:54:45][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 20:54:45][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 20:54:49][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 20:54:49][INFO][UIAutomation] Clicked at position (654, 496)[0m
[32m[2025-05-22 20:54:49][INFO][UIAutomation] Clicked top-right at (654, 496)[0m
[32m[2025-05-22 20:54:53][INFO][UIAutomation] Clicked at position (656, 498)[0m
[32m[2025-05-22 20:54:53][INFO][UIAutomation] Clicked top-right at (656, 498)[0m
[32m[2025-05-22 20:54:53][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 20:54:58][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-22 20:54:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 20:54:58][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 20:54:59][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 20:54:59][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 20:54:59][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 20:56:14][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 20:56:19][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 20:56:19][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 20:56:19][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 20:56:19][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 20:56:24][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 20:56:24][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 20:56:24][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 20:56:28][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 20:56:29][INFO][UIAutomation] Clicked at position (654, 496)[0m
[32m[2025-05-22 20:56:29][INFO][UIAutomation] Clicked top-right at (654, 496)[0m
[32m[2025-05-22 20:56:29][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 20:56:33][INFO][UIAutomation] Clicked at position (656, 498)[0m
[32m[2025-05-22 20:56:33][INFO][UIAutomation] Clicked top-right at (656, 498)[0m
[32m[2025-05-22 20:56:33][INFO][UIAutomation] Typed text: 8[0m
[32m[2025-05-22 20:56:38][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 20:56:38][INFO][UIAutomation] Clicked at position (1042, 345)[0m
[32m[2025-05-22 20:56:38][INFO][UIAutomation] Clicked center at (1042, 345)[0m
[32m[2025-05-22 20:56:38][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-05-22 20:56:38][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 20:56:38][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:00:25][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:00:30][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:00:30][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:00:30][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:00:30][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:00:35][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 21:00:35][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 21:00:35][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 21:00:39][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 21:00:39][INFO][UIAutomation] Clicked at position (654, 496)[0m
[32m[2025-05-22 21:00:39][INFO][UIAutomation] Clicked top-right at (654, 496)[0m
[32m[2025-05-22 21:00:48][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 21:00:53][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 21:00:53][INFO][UIAutomation] Clicked at position (1042, 345)[0m
[32m[2025-05-22 21:00:53][INFO][UIAutomation] Clicked center at (1042, 345)[0m
[32m[2025-05-22 21:00:53][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-05-22 21:00:53][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:00:53][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:01:17][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:01:22][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:01:22][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:01:22][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:01:22][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:01:27][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 21:01:27][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 21:01:27][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 21:01:31][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 21:01:31][INFO][UIAutomation] Clicked at position (656, 498)[0m
[32m[2025-05-22 21:01:31][INFO][UIAutomation] Clicked top-right at (656, 498)[0m
[32m[2025-05-22 21:01:40][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 21:01:45][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 21:01:45][INFO][UIAutomation] Clicked at position (1042, 345)[0m
[32m[2025-05-22 21:01:45][INFO][UIAutomation] Clicked center at (1042, 345)[0m
[32m[2025-05-22 21:01:45][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-05-22 21:01:45][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:01:45][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:02:36][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:02:41][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:02:41][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:02:41][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:02:41][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:02:46][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 21:02:46][INFO][UIAutomation] Clicked at position (931, 345)[0m
[32m[2025-05-22 21:02:46][INFO][UIAutomation] Clicked center at (931, 345)[0m
[32m[2025-05-22 21:02:50][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 21:02:50][INFO][UIAutomation] Clicked at position (656, 498)[0m
[32m[2025-05-22 21:02:50][INFO][UIAutomation] Clicked top-right at (656, 498)[0m
[32m[2025-05-22 21:02:58][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 21:03:03][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 21:03:04][INFO][UIAutomation] Clicked at position (1042, 345)[0m
[32m[2025-05-22 21:03:04][INFO][UIAutomation] Clicked center at (1042, 345)[0m
[32m[2025-05-22 21:03:04][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-05-22 21:03:04][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:03:04][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:13:18][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:13:23][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:13:23][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:13:23][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:13:23][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:13:28][INFO][UIAutomation] 单击勾选用例[0m
[32m[2025-05-22 21:13:28][INFO][UIAutomation] rightClick at position (254, 242)[0m
[32m[2025-05-22 21:13:28][INFO][UIAutomation] Clicked center at (254, 242)[0m
[32m[2025-05-22 21:13:28][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 21:13:28][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 21:13:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 21:13:29][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:13:29][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:20:56][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:21:01][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:21:01][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:21:01][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:21:01][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:21:06][INFO][UIAutomation] 右键勾选用例[0m
[32m[2025-05-22 21:21:06][INFO][UIAutomation] rightClick at position (254, 242)[0m
[32m[2025-05-22 21:21:06][INFO][UIAutomation] Clicked center at (254, 242)[0m
[32m[2025-05-22 21:21:06][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 21:21:08][INFO][UIAutomation] 勾选SMD_collections_path[0m
[32m[2025-05-22 21:21:09][INFO][UIAutomation] Clicked at position (436, 452)[0m
[32m[2025-05-22 21:21:09][INFO][UIAutomation] Clicked center at (436, 452)[0m
[32m[2025-05-22 21:21:09][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 21:21:11][INFO][UIAutomation] 勾选Marks_from_path[0m
[32m[2025-05-22 21:21:11][INFO][UIAutomation] Clicked at position (745, 524)[0m
[32m[2025-05-22 21:21:11][INFO][UIAutomation] Clicked center at (745, 524)[0m
[32m[2025-05-22 21:21:11][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 21:21:13][INFO][UIAutomation] 勾选NB_MAX[0m
[32m[2025-05-22 21:21:13][INFO][UIAutomation] Clicked at position (967, 548)[0m
[32m[2025-05-22 21:21:13][INFO][UIAutomation] Clicked center at (967, 548)[0m
[32m[2025-05-22 21:21:13][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 21:21:13][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 21:21:14][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 21:21:14][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:21:14][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:23:17][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:23:22][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:23:22][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:23:22][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:23:22][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:23:24][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:23:26][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:23:26][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:23:26][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:23:28][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-05-22 21:23:29][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:23:31][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-05-22 21:23:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:23:31][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:23:35][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 21:23:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:23:37][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-22 21:23:37][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:23:39][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-05-22 21:23:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:23:41][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:23:41][INFO][UIAutomation] Clicked at position (1132, 422)[0m
[32m[2025-05-22 21:23:41][INFO][UIAutomation] Clicked center at (1132, 422)[0m
[32m[2025-05-22 21:23:43][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-05-22 21:23:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:23:45][INFO][UIAutomation] 单击ok[0m
[31m[2025-05-22 21:23:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:23:47][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 21:26:18][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:26:23][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:26:23][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:26:23][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:26:23][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:26:25][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:26:28][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-05-22 21:26:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:26:30][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-05-22 21:26:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:26:32][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-05-22 21:26:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:26:32][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:26:38][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:26:43][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:26:43][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:26:43][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:26:43][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:26:45][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:26:47][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:26:48][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:26:48][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:26:50][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:26:50][INFO][UIAutomation] Clicked at position (411, 301)[0m
[32m[2025-05-22 21:26:50][INFO][UIAutomation] Clicked center at (411, 301)[0m
[32m[2025-05-22 21:26:52][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 21:26:52][INFO][UIAutomation] Clicked at position (717, 386)[0m
[32m[2025-05-22 21:26:52][INFO][UIAutomation] Clicked center at (717, 386)[0m
[32m[2025-05-22 21:26:52][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:26:56][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 21:26:56][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:26:58][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-22 21:26:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:27:00][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-05-22 21:27:00][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:27:02][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:27:03][INFO][UIAutomation] Clicked at position (1132, 422)[0m
[32m[2025-05-22 21:27:03][INFO][UIAutomation] Clicked center at (1132, 422)[0m
[32m[2025-05-22 21:27:05][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-05-22 21:27:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:27:07][INFO][UIAutomation] 单击ok[0m
[31m[2025-05-22 21:27:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:27:09][INFO][UIAutomation] 单击Connect[0m
[31m[2025-05-22 21:27:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:27:14][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-22 21:27:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:27:18][INFO][UIAutomation] 选择音量8/15[0m
[31m[2025-05-22 21:27:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:02][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:29:07][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:29:07][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:29:07][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:29:07][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:29:09][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:29:11][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:29:11][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:29:11][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:29:13][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:29:13][INFO][UIAutomation] Clicked at position (411, 301)[0m
[32m[2025-05-22 21:29:13][INFO][UIAutomation] Clicked center at (411, 301)[0m
[32m[2025-05-22 21:29:15][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 21:29:16][INFO][UIAutomation] Clicked at position (717, 386)[0m
[32m[2025-05-22 21:29:16][INFO][UIAutomation] Clicked center at (717, 386)[0m
[32m[2025-05-22 21:29:16][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:29:20][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 21:29:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:22][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-22 21:29:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:24][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-05-22 21:29:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:26][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:29:26][INFO][UIAutomation] Clicked at position (1132, 422)[0m
[32m[2025-05-22 21:29:26][INFO][UIAutomation] Clicked center at (1132, 422)[0m
[32m[2025-05-22 21:29:28][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-05-22 21:29:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:30][INFO][UIAutomation] 单击ok[0m
[31m[2025-05-22 21:29:30][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:32][INFO][UIAutomation] 单击Connect[0m
[31m[2025-05-22 21:29:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:38][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-22 21:29:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:42][INFO][UIAutomation] 选择音量8/15[0m
[31m[2025-05-22 21:29:42][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:50][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 21:29:55][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-22 21:29:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:29:55][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 21:33:09][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:33:14][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:33:14][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:33:14][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:33:14][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:33:16][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 21:33:16][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:33:18][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-22 21:33:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:33:20][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-05-22 21:33:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:33:22][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-05-22 21:33:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:33:24][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-05-22 21:33:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:33:26][INFO][UIAutomation] 单击ok[0m
[31m[2025-05-22 21:33:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:33:28][INFO][UIAutomation] 单击Connect[0m
[31m[2025-05-22 21:33:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:33:33][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-22 21:33:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:33:37][INFO][UIAutomation] 选择音量8/15[0m
[31m[2025-05-22 21:33:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:36:41][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:36:46][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:36:46][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:36:46][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:36:46][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:36:48][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 21:36:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:36:48][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 21:36:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 21:36:49][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:36:49][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:38:39][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:38:44][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:38:44][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:38:44][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:38:44][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:38:46][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 21:38:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:38:46][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 21:38:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 21:38:47][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:38:47][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:42:12][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:42:17][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:42:17][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:42:17][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:42:17][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:42:19][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 21:42:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:42:19][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 21:42:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 21:42:20][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:42:20][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:43:11][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:43:16][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:43:16][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:43:16][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:43:16][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:43:18][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:43:19][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:43:19][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 21:43:19][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 21:43:19][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 21:43:19][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:43:19][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 21:43:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 21:43:20][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 21:43:20][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 21:47:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:47:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:47:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:47:06][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:47:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:47:08][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:47:10][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:47:11][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:11][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:47:11][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:47:13][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:47:13][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:13][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 21:47:13][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 21:47:15][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 21:47:15][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:15][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 21:47:15][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 21:47:15][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:47:19][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 21:47:19][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:19][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 21:47:19][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 21:47:21][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 21:47:21][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:22][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 21:47:22][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 21:47:24][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 21:47:24][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:24][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 21:47:24][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 21:47:26][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:47:26][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:26][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 21:47:26][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 21:47:28][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 21:47:28][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:28][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 21:47:28][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 21:47:30][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 21:47:30][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:30][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 21:47:30][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 21:47:32][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 21:47:33][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:33][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 21:47:33][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 21:47:38][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 21:47:38][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:38][INFO][UIAutomation] Clicked at position (927, 283)[0m
[32m[2025-05-22 21:47:38][INFO][UIAutomation] Clicked center at (927, 283)[0m
[32m[2025-05-22 21:47:42][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 21:47:42][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:42][INFO][UIAutomation] Clicked at position (588, 444)[0m
[32m[2025-05-22 21:47:42][INFO][UIAutomation] Clicked top-right at (588, 444)[0m
[32m[2025-05-22 21:47:50][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 21:47:55][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 21:47:55][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:47:56][INFO][UIAutomation] Clicked at position (1038, 283)[0m
[32m[2025-05-22 21:47:56][INFO][UIAutomation] Clicked center at (1038, 283)[0m
[32m[2025-05-22 21:47:56][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 21:49:07][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:49:12][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:49:12][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:49:12][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:49:12][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:49:14][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:49:16][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:49:16][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:17][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:49:17][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:49:19][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:49:19][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:19][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 21:49:19][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 21:49:21][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 21:49:21][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:21][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 21:49:21][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 21:49:21][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:49:25][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 21:49:25][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:25][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 21:49:25][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 21:49:27][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 21:49:27][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:28][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 21:49:28][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 21:49:30][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 21:49:30][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:30][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 21:49:30][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 21:49:32][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:49:32][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:32][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 21:49:32][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 21:49:34][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 21:49:34][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:34][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 21:49:34][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 21:49:36][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 21:49:36][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:36][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 21:49:36][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 21:49:38][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 21:49:39][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:39][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 21:49:39][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 21:49:44][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 21:49:44][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:44][INFO][UIAutomation] Clicked at position (927, 283)[0m
[32m[2025-05-22 21:49:44][INFO][UIAutomation] Clicked center at (927, 283)[0m
[32m[2025-05-22 21:49:54][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 21:49:54][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:49:54][INFO][UIAutomation] Clicked at position (650, 434)[0m
[32m[2025-05-22 21:49:54][INFO][UIAutomation] Clicked top-right at (650, 434)[0m
[32m[2025-05-22 21:50:02][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 21:50:07][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 21:50:07][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:50:08][INFO][UIAutomation] Clicked at position (1038, 283)[0m
[32m[2025-05-22 21:50:08][INFO][UIAutomation] Clicked center at (1038, 283)[0m
[32m[2025-05-22 21:50:08][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 21:50:15][INFO][UIAutomation] 右键勾选用例[0m
[31m[2025-05-22 21:50:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:50:15][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 21:51:19][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:51:24][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:51:24][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:51:24][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:51:24][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:51:26][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:51:28][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:51:28][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:28][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:51:28][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:51:30][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:51:30][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:30][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 21:51:30][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 21:51:32][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 21:51:32][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:33][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 21:51:33][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 21:51:33][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:51:37][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 21:51:37][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:37][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 21:51:37][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 21:51:39][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 21:51:39][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:39][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 21:51:39][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 21:51:41][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 21:51:41][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:41][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 21:51:41][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 21:51:43][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:51:43][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:44][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 21:51:44][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 21:51:46][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 21:51:46][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:46][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 21:51:46][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 21:51:48][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 21:51:48][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:48][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 21:51:48][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 21:51:50][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 21:51:50][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:50][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 21:51:50][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 21:51:55][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 21:51:55][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:51:55][INFO][UIAutomation] Clicked at position (927, 283)[0m
[32m[2025-05-22 21:51:55][INFO][UIAutomation] Clicked center at (927, 283)[0m
[32m[2025-05-22 21:51:59][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 21:51:59][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:52:00][INFO][UIAutomation] Clicked at position (650, 10.5)[0m
[32m[2025-05-22 21:52:00][INFO][UIAutomation] Clicked top-right at (650, 10.5)[0m
[32m[2025-05-22 21:52:08][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 21:52:13][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 21:52:13][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:52:13][INFO][UIAutomation] Clicked at position (1038, 283)[0m
[32m[2025-05-22 21:52:13][INFO][UIAutomation] Clicked center at (1038, 283)[0m
[32m[2025-05-22 21:52:13][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 21:52:20][INFO][UIAutomation] 右键勾选用例[0m
[31m[2025-05-22 21:52:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:52:20][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 21:55:25][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:55:30][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:55:30][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:55:30][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:55:30][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:55:32][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:55:34][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:55:34][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:34][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:55:34][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:55:36][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:55:36][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:37][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 21:55:37][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 21:55:39][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 21:55:39][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:39][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 21:55:39][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 21:55:39][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:55:43][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 21:55:43][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:44][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 21:55:44][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 21:55:46][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 21:55:46][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:46][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 21:55:46][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 21:55:48][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 21:55:48][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:49][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 21:55:49][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 21:55:51][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:55:51][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:51][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 21:55:51][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 21:55:53][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 21:55:53][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:54][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 21:55:54][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 21:55:56][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 21:55:56][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:57][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 21:55:57][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 21:55:59][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 21:55:59][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:55:59][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 21:55:59][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 21:56:04][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-22 21:56:05][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:56:09][INFO][UIAutomation] 选择音量8/15[0m
[31m[2025-05-22 21:56:09][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:56:30][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:56:35][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:56:35][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:56:35][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:56:35][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:56:37][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:56:39][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:56:40][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:56:40][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:56:40][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:56:42][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:56:42][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:56:43][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 21:56:43][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 21:56:45][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 21:56:45][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:56:45][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 21:56:45][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 21:56:45][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:56:49][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 21:56:49][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:56:50][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 21:56:50][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 21:56:52][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 21:56:53][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:56:53][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 21:56:53][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 21:56:55][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 21:56:55][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:56:56][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 21:56:56][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 21:56:58][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:56:58][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:56:59][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 21:56:59][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 21:57:01][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 21:57:01][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:57:02][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 21:57:02][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 21:57:04][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 21:57:04][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:57:05][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 21:57:05][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 21:57:07][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 21:57:08][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:57:09][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 21:57:09][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 21:57:14][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-22 21:57:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:57:18][INFO][UIAutomation] 选择音量8/15[0m
[31m[2025-05-22 21:57:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:57:36][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 21:57:41][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 21:57:41][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 21:57:41][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 21:57:41][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 21:57:43][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 21:57:45][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 21:57:45][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:57:45][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 21:57:45][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 21:57:47][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 21:57:47][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:57:49][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 21:57:49][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 21:57:51][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 21:57:51][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:57:51][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 21:57:51][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 21:57:51][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 21:57:55][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 21:57:55][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:57:56][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 21:57:56][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 21:57:58][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 21:57:58][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:57:58][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 21:57:58][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 21:58:00][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 21:58:00][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:58:02][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 21:58:02][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 21:58:04][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 21:58:04][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:58:05][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 21:58:05][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 21:58:07][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 21:58:07][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:58:08][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 21:58:08][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 21:58:10][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 21:58:10][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:58:11][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 21:58:11][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 21:58:13][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 21:58:13][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:58:14][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 21:58:14][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 21:58:19][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-22 21:58:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:58:24][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 21:58:24][INFO][UIAutomation] test[0m
[32m[2025-05-22 21:58:25][INFO][UIAutomation] Clicked at position (650, 217.0)[0m
[32m[2025-05-22 21:58:25][INFO][UIAutomation] Clicked top-right at (650, 217.0)[0m
[32m[2025-05-22 21:58:33][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 21:58:38][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-05-22 21:58:38][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 21:58:38][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 22:00:19][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 22:00:24][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 22:00:24][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 22:00:24][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 22:00:24][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 22:00:26][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 22:00:28][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 22:00:28][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:28][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 22:00:28][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 22:00:30][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 22:00:30][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:31][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 22:00:31][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 22:00:33][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 22:00:33][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:33][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 22:00:33][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 22:00:33][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 22:00:38][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 22:00:38][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:39][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 22:00:39][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 22:00:41][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 22:00:41][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:41][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 22:00:41][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 22:00:43][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 22:00:43][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:44][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 22:00:44][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 22:00:46][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 22:00:46][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:47][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 22:00:47][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 22:00:49][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 22:00:49][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:51][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 22:00:51][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 22:00:53][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 22:00:53][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:54][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 22:00:54][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 22:00:56][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 22:00:56][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:00:57][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 22:00:57][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 22:01:07][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 22:01:07][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:01:08][INFO][UIAutomation] Clicked at position (927, 283)[0m
[32m[2025-05-22 22:01:08][INFO][UIAutomation] Clicked center at (927, 283)[0m
[32m[2025-05-22 22:01:12][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 22:01:12][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:01:13][INFO][UIAutomation] Clicked at position (650, 217.0)[0m
[32m[2025-05-22 22:01:13][INFO][UIAutomation] Clicked top-right at (650, 217.0)[0m
[32m[2025-05-22 22:01:22][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 22:01:27][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 22:01:27][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:01:28][INFO][UIAutomation] Clicked at position (1038, 283)[0m
[32m[2025-05-22 22:01:28][INFO][UIAutomation] Clicked center at (1038, 283)[0m
[32m[2025-05-22 22:01:28][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 22:05:21][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 22:05:26][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 22:05:26][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 22:05:26][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 22:05:26][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 22:05:28][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 22:05:31][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 22:05:31][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:05:31][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 22:05:31][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 22:05:33][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-05-22 22:05:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:35][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-05-22 22:05:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:35][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 22:05:39][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 22:05:39][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:41][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-22 22:05:41][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:43][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-05-22 22:05:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:45][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[31m[2025-05-22 22:05:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:47][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-05-22 22:05:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:50][INFO][UIAutomation] 单击ok[0m
[31m[2025-05-22 22:05:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:52][INFO][UIAutomation] 单击Connect[0m
[31m[2025-05-22 22:05:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:05:58][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 22:06:07][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 22:06:12][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 22:06:12][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 22:06:12][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 22:06:12][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 22:06:14][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 22:06:16][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 22:06:16][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:06:16][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 22:06:16][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 22:06:18][INFO][UIAutomation] 单击选择蓝牙设备[0m
[31m[2025-05-22 22:06:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:06:20][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-05-22 22:06:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:06:20][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 22:06:24][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-05-22 22:06:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:06:26][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-05-22 22:06:27][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:06:42][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 22:06:47][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 22:06:47][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 22:06:47][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 22:06:47][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 22:06:49][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 22:06:51][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 22:06:51][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:06:51][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 22:06:51][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 22:06:53][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 22:06:53][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:06:55][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 22:06:55][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 22:06:57][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 22:06:57][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:06:57][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 22:06:57][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 22:06:57][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 22:07:01][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 22:07:01][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:07:02][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 22:07:02][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 22:07:04][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 22:07:04][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:07:04][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 22:07:04][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 22:07:06][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 22:07:06][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:07:08][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 22:07:08][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 22:07:10][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 22:07:10][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:07:11][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 22:07:11][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 22:07:13][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 22:07:13][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:07:14][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 22:07:14][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 22:07:16][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 22:07:16][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:07:17][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 22:07:17][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 22:07:19][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 22:07:19][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:07:20][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 22:07:20][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 22:07:30][INFO][UIAutomation] 单击Ring[0m
[31m[2025-05-22 22:07:31][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:07:35][INFO][UIAutomation] 选择音量8/15[0m
[31m[2025-05-22 22:07:35][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-05-22 22:08:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 22:08:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 22:08:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 22:08:06][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 22:08:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 22:08:08][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 22:08:10][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 22:08:10][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:10][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 22:08:10][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 22:08:12][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 22:08:12][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:13][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 22:08:13][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 22:08:15][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 22:08:16][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:16][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 22:08:16][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 22:08:16][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 22:08:20][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 22:08:20][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:21][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 22:08:21][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 22:08:23][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 22:08:23][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:23][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 22:08:23][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 22:08:25][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 22:08:25][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:26][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 22:08:26][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 22:08:28][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 22:08:29][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:30][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 22:08:30][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 22:08:32][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 22:08:32][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:33][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 22:08:33][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 22:08:35][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 22:08:35][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:36][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 22:08:36][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 22:08:38][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 22:08:38][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:39][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 22:08:39][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 22:08:49][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 22:08:49][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:51][INFO][UIAutomation] Clicked at position (927, 283)[0m
[32m[2025-05-22 22:08:51][INFO][UIAutomation] Clicked center at (927, 283)[0m
[32m[2025-05-22 22:08:55][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 22:08:55][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:08:56][INFO][UIAutomation] Clicked at position (650, 444.5)[0m
[32m[2025-05-22 22:08:56][INFO][UIAutomation] Clicked top-right at (650, 444.5)[0m
[32m[2025-05-22 22:09:04][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 22:09:09][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 22:09:09][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:09:10][INFO][UIAutomation] Clicked at position (1038, 283)[0m
[32m[2025-05-22 22:09:10][INFO][UIAutomation] Clicked center at (1038, 283)[0m
[32m[2025-05-22 22:09:10][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 22:09:17][INFO][UIAutomation] 右键勾选用例[0m
[32m[2025-05-22 22:09:18][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:09:18][INFO][UIAutomation] rightClick at position (254, 242)[0m
[32m[2025-05-22 22:09:18][INFO][UIAutomation] Clicked center at (254, 242)[0m
[32m[2025-05-22 22:09:18][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 22:09:20][INFO][UIAutomation] 勾选SMD_collections_path[0m
[32m[2025-05-22 22:09:20][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:09:21][INFO][UIAutomation] Clicked at position (436, 452)[0m
[32m[2025-05-22 22:09:21][INFO][UIAutomation] Clicked center at (436, 452)[0m
[32m[2025-05-22 22:09:21][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 22:09:23][INFO][UIAutomation] 勾选Marks_from_path[0m
[32m[2025-05-22 22:09:23][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:09:24][INFO][UIAutomation] Clicked at position (745, 524)[0m
[32m[2025-05-22 22:09:24][INFO][UIAutomation] Clicked center at (745, 524)[0m
[32m[2025-05-22 22:09:24][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 22:09:26][INFO][UIAutomation] 勾选NB_MAX[0m
[32m[2025-05-22 22:09:26][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:09:28][INFO][UIAutomation] Clicked at position (967, 548)[0m
[32m[2025-05-22 22:09:28][INFO][UIAutomation] Clicked center at (967, 548)[0m
[32m[2025-05-22 22:09:28][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 22:09:33][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-22 22:09:33][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 22:09:34][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 22:09:34][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 22:09:34][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-05-22 22:11:55][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-05-22 22:12:00][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-05-22 22:12:00][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-05-22 22:12:00][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-05-22 22:12:00][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-05-22 22:12:02][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-05-22 22:12:04][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-05-22 22:12:04][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:04][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-05-22 22:12:04][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-05-22 22:12:06][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-05-22 22:12:06][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:07][INFO][UIAutomation] Clicked at position (407, 239)[0m
[32m[2025-05-22 22:12:07][INFO][UIAutomation] Clicked center at (407, 239)[0m
[32m[2025-05-22 22:12:09][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-05-22 22:12:09][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:10][INFO][UIAutomation] Clicked at position (713, 324)[0m
[32m[2025-05-22 22:12:10][INFO][UIAutomation] Clicked center at (713, 324)[0m
[32m[2025-05-22 22:12:10][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-05-22 22:12:14][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-05-22 22:12:14][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:15][INFO][UIAutomation] Clicked at position (406, 261)[0m
[32m[2025-05-22 22:12:15][INFO][UIAutomation] Clicked center at (406, 261)[0m
[32m[2025-05-22 22:12:17][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-05-22 22:12:17][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:17][INFO][UIAutomation] Clicked at position (638, 422)[0m
[32m[2025-05-22 22:12:17][INFO][UIAutomation] Clicked center at (638, 422)[0m
[32m[2025-05-22 22:12:19][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-05-22 22:12:19][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:20][INFO][UIAutomation] Clicked at position (959, 263)[0m
[32m[2025-05-22 22:12:20][INFO][UIAutomation] Clicked center at (959, 263)[0m
[32m[2025-05-22 22:12:22][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-05-22 22:12:22][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:23][INFO][UIAutomation] Clicked at position (1136, 625)[0m
[32m[2025-05-22 22:12:23][INFO][UIAutomation] Clicked center at (1136, 625)[0m
[32m[2025-05-22 22:12:25][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-05-22 22:12:26][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:27][INFO][UIAutomation] Clicked at position (1119, 657)[0m
[32m[2025-05-22 22:12:27][INFO][UIAutomation] Clicked center at (1119, 657)[0m
[32m[2025-05-22 22:12:29][INFO][UIAutomation] 单击ok[0m
[32m[2025-05-22 22:12:29][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:30][INFO][UIAutomation] Clicked at position (1090, 791)[0m
[32m[2025-05-22 22:12:30][INFO][UIAutomation] Clicked center at (1090, 791)[0m
[32m[2025-05-22 22:12:32][INFO][UIAutomation] 单击Connect[0m
[32m[2025-05-22 22:12:32][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:33][INFO][UIAutomation] Clicked at position (392, 281)[0m
[32m[2025-05-22 22:12:33][INFO][UIAutomation] Clicked center at (392, 281)[0m
[32m[2025-05-22 22:12:43][INFO][UIAutomation] 单击Ring[0m
[32m[2025-05-22 22:12:43][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:44][INFO][UIAutomation] Clicked at position (927, 283)[0m
[32m[2025-05-22 22:12:44][INFO][UIAutomation] Clicked center at (927, 283)[0m
[32m[2025-05-22 22:12:48][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-05-22 22:12:48][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:12:49][INFO][UIAutomation] Clicked at position (650, 444.5)[0m
[32m[2025-05-22 22:12:49][INFO][UIAutomation] Clicked top-right at (650, 444.5)[0m
[32m[2025-05-22 22:12:58][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-05-22 22:13:03][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-05-22 22:13:03][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:13:04][INFO][UIAutomation] Clicked at position (1038, 283)[0m
[32m[2025-05-22 22:13:04][INFO][UIAutomation] Clicked center at (1038, 283)[0m
[32m[2025-05-22 22:13:04][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-05-22 22:13:11][INFO][UIAutomation] 右键勾选用例[0m
[32m[2025-05-22 22:13:11][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:13:12][INFO][UIAutomation] rightClick at position (254, 242)[0m
[32m[2025-05-22 22:13:12][INFO][UIAutomation] Clicked center at (254, 242)[0m
[32m[2025-05-22 22:13:12][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 22:13:14][INFO][UIAutomation] 勾选SMD_collections_path[0m
[32m[2025-05-22 22:13:14][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:13:15][INFO][UIAutomation] Clicked at position (436, 452)[0m
[32m[2025-05-22 22:13:15][INFO][UIAutomation] Clicked center at (436, 452)[0m
[32m[2025-05-22 22:13:15][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 22:13:17][INFO][UIAutomation] 勾选Marks_from_path[0m
[32m[2025-05-22 22:13:17][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:13:18][INFO][UIAutomation] Clicked at position (745, 524)[0m
[32m[2025-05-22 22:13:18][INFO][UIAutomation] Clicked center at (745, 524)[0m
[32m[2025-05-22 22:13:18][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 22:13:20][INFO][UIAutomation] 勾选NB_MAX[0m
[32m[2025-05-22 22:13:20][INFO][UIAutomation] test[0m
[32m[2025-05-22 22:13:21][INFO][UIAutomation] Clicked at position (967, 548)[0m
[32m[2025-05-22 22:13:21][INFO][UIAutomation] Clicked center at (967, 548)[0m
[32m[2025-05-22 22:13:21][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-05-22 22:13:26][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-05-22 22:13:26][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-05-22 22:13:27][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-05-22 22:13:27][INFO][UIAutomation] All threads stopped[0m
[32m[2025-05-22 22:13:27][INFO][UIAutomation] UI automation framework finished successfully[0m
