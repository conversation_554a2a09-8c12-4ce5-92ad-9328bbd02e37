import os

# 圖片資源文件夾路徑（根據實際情況修改）
# RESOURCE_DIR = r"D:/linyu/autotest/Testcode/resource/picture"
RESOURCE_DIR = r"D:/python_code/pc_ui_auto_svncode/resource/picture"

class ImageResources:
    # ljw = os.path.join(RESOURCE_DIR, "ljw.png")
    # bluetooth_audio = os.path.join(RESOURCE_DIR, "bluetooth.png")
    # selected_device = os.path.join(RESOURCE_DIR, "selected_device.png")
    # auto_device = os.path.join(RESOURCE_DIR, "auto_device.png")
    # Activated_Profile = os.path.join(RESOURCE_DIR, "Activated_Profile.png")
    # HFP_AG = os.path.join(RESOURCE_DIR, "HFP_AG.png")
    # Profile_Settings = os.path.join(RESOURCE_DIR, "Profile_Settings.png")
    # Select_msbccvsd = os.path.join(RESOURCE_DIR, "Select_msbccvsd.png")
    # msbc = os.path.join(RESOURCE_DIR, "msbc.png")
    # msbc_blue = os.path.join(RESOURCE_DIR, "msbc_blue.png")
    # HFP_AG_Settings_ok = os.path.join(RESOURCE_DIR, "HFP_AG_Settings_ok.png")
    # Connect = os.path.join(RESOURCE_DIR, "Connect.png")
    # Ring = os.path.join(RESOURCE_DIR, "Ring.png")
    # Accept_Call = os.path.join(RESOURCE_DIR, "Accept_Call.png")
    # Close = os.path.join(RESOURCE_DIR, "Close.png")
    # close1 = os.path.join(RESOURCE_DIR, "close1.png")
    # close2 = os.path.join(RESOURCE_DIR, "close2.png")
    # volume8 = os.path.join(RESOURCE_DIR, "volume8.png")
    # volume15 = os.path.join(RESOURCE_DIR, "volume15.png")
    # case1 = os.path.join(RESOURCE_DIR, "case1.png")
    # casetest = os.path.join(RESOURCE_DIR, "casetest.png")
    # casetest1 = os.path.join(RESOURCE_DIR, "casetest1.png")
    # Define_Setup = os.path.join(RESOURCE_DIR, "Define_Setup.png")
    # SMD_collections = os.path.join(RESOURCE_DIR, "SMD_collections.png")
    # Marks_from = os.path.join(RESOURCE_DIR, "Marks_from.png")
    # NB_MAX = os.path.join(RESOURCE_DIR, "NB_MAX.png")
    # Define_Setup_ok = os.path.join(RESOURCE_DIR, "Define_ok.png")
    # WB_MAX = os.path.join(RESOURCE_DIR, "WB_MAX.png")
    # D_RCV_NET = os.path.join(RESOURCE_DIR, "D_RCV_NET.png")
    # D_SND_NET = os.path.join(RESOURCE_DIR, "D_SND_NET.png")
    # D_SR_REA = os.path.join(RESOURCE_DIR, "D_SR_REA.png")
    # Edit_correction_ok = os.path.join(RESOURCE_DIR, "Edit_correction_ok.png")
    # Edit_varrible_ok = os.path.join(RESOURCE_DIR, "Edit_varrible_ok.png")
    # bluetooth_right = os.path.join(RESOURCE_DIR, "bluetooth_right.png")
    # bluetooth_3quest = os.path.join(RESOURCE_DIR, "bluetooth_3quest.png")
    # WB_3QUEST = os.path.join(RESOURCE_DIR, "WB_3QUEST.png")
    # A3quest_ok = os.path.join(RESOURCE_DIR, "A3quest_ok.png")
    # choose_3QUEST = os.path.join(RESOURCE_DIR, "choose_3QUEST.png")
    # HAE = os.path.join(RESOURCE_DIR, "HAE.png")
    # lastHEWB = os.path.join(RESOURCE_DIR, "lastHEWB.png")
    # finish_HEWB = os.path.join(RESOURCE_DIR, "finish_HEWB.png")

    @classmethod
    def get(cls, name):
        return getattr(cls, name, None)
