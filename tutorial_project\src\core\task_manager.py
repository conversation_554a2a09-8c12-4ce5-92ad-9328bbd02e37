"""
任务管理和多线程执行模块
提供任务队列、线程池、任务调度等功能
"""

import threading
import time
from queue import Queue, Empty
from typing import Callable, Any, List, Optional
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, Future

from .logger import get_logger


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待执行
    RUNNING = "running"      # 正在执行
    COMPLETED = "completed"  # 执行完成
    FAILED = "failed"        # 执行失败
    CANCELLED = "cancelled"  # 已取消


@dataclass
class Task:
    """任务数据类"""
    id: str
    name: str
    func: Callable
    args: tuple = ()
    kwargs: dict = None
    priority: int = 0  # 优先级，数字越大优先级越高
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Exception = None
    start_time: float = None
    end_time: float = None
    
    def __post_init__(self):
        if self.kwargs is None:
            self.kwargs = {}
    
    @property
    def duration(self) -> Optional[float]:
        """获取任务执行时长"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class TaskManager:
    """任务管理器"""
    
    def __init__(self, max_workers: int = 3):
        """
        初始化任务管理器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.max_workers = max_workers
        self.logger = get_logger("TaskManager")
        
        # 任务队列和存储
        self.task_queue = Queue()
        self.tasks = {}  # 任务ID -> Task映射
        self.running_tasks = {}  # 正在运行的任务
        
        # 线程池和控制
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.futures = {}  # Future对象映射
        self.running = False
        self.worker_thread = None
        
        # 统计信息
        self.total_tasks = 0
        self.completed_tasks = 0
        self.failed_tasks = 0
        
        self.logger.info(f"任务管理器初始化完成，最大工作线程数: {max_workers}")
    
    def add_task(self, task_id: str, name: str, func: Callable, 
                *args, priority: int = 0, **kwargs) -> Task:
        """
        添加任务到队列
        
        Args:
            task_id: 任务唯一标识
            name: 任务名称
            func: 要执行的函数
            args: 函数参数
            priority: 优先级
            kwargs: 函数关键字参数
            
        Returns:
            创建的任务对象
        """
        task = Task(
            id=task_id,
            name=name,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority
        )
        
        self.tasks[task_id] = task
        self.task_queue.put((priority, task))
        self.total_tasks += 1
        
        self.logger.info(f"添加任务: {name} (ID: {task_id}, 优先级: {priority})")
        return task
    
    def start(self):
        """启动任务管理器"""
        if self.running:
            self.logger.warning("任务管理器已在运行")
            return
        
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
        
        self.logger.info("任务管理器已启动")
    
    def stop(self, wait_for_completion: bool = True):
        """
        停止任务管理器
        
        Args:
            wait_for_completion: 是否等待当前任务完成
        """
        self.running = False
        
        if wait_for_completion:
            # 等待所有任务完成
            self.executor.shutdown(wait=True)
        else:
            # 立即停止所有任务
            self.executor.shutdown(wait=False)
        
        if self.worker_thread:
            self.worker_thread.join(timeout=5)
        
        self.logger.info("任务管理器已停止")
    
    def _worker_loop(self):
        """工作线程主循环"""
        while self.running:
            try:
                # 从队列获取任务（优先级队列）
                priority, task = self.task_queue.get(timeout=1)
                
                if task.status == TaskStatus.CANCELLED:
                    continue
                
                # 提交任务到线程池
                future = self.executor.submit(self._execute_task, task)
                self.futures[task.id] = future
                
                # 标记任务为运行状态
                task.status = TaskStatus.RUNNING
                task.start_time = time.time()
                self.running_tasks[task.id] = task
                
                self.logger.info(f"开始执行任务: {task.name} (ID: {task.id})")
                
            except Empty:
                # 队列为空，继续循环
                continue
            except Exception as e:
                self.logger.error(f"工作线程异常: {e}")
    
    def _execute_task(self, task: Task):
        """
        执行单个任务
        
        Args:
            task: 要执行的任务
        """
        try:
            # 执行任务函数
            result = task.func(*task.args, **task.kwargs)
            
            # 更新任务状态
            task.result = result
            task.status = TaskStatus.COMPLETED
            task.end_time = time.time()
            
            # 更新统计
            self.completed_tasks += 1
            
            self.logger.info(f"任务执行完成: {task.name} (ID: {task.id}), "
                           f"耗时: {task.duration:.2f}秒")
            
        except Exception as e:
            # 任务执行失败
            task.error = e
            task.status = TaskStatus.FAILED
            task.end_time = time.time()
            
            # 更新统计
            self.failed_tasks += 1
            
            self.logger.error(f"任务执行失败: {task.name} (ID: {task.id}), "
                            f"错误: {e}")
        
        finally:
            # 从运行任务列表中移除
            if task.id in self.running_tasks:
                del self.running_tasks[task.id]
            
            # 清理Future对象
            if task.id in self.futures:
                del self.futures[task.id]
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """
        获取任务信息
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务对象，如果不存在返回None
        """
        return self.tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        task = self.tasks.get(task_id)
        if not task:
            return False
        
        if task.status == TaskStatus.PENDING:
            task.status = TaskStatus.CANCELLED
            self.logger.info(f"任务已取消: {task.name} (ID: {task_id})")
            return True
        elif task.status == TaskStatus.RUNNING:
            # 尝试取消正在运行的任务
            future = self.futures.get(task_id)
            if future and future.cancel():
                task.status = TaskStatus.CANCELLED
                self.logger.info(f"正在运行的任务已取消: {task.name} (ID: {task_id})")
                return True
        
        return False
    
    def wait_for_completion(self, timeout: Optional[float] = None):
        """
        等待所有任务完成
        
        Args:
            timeout: 超时时间（秒）
        """
        start_time = time.time()
        
        while self.running_tasks or not self.task_queue.empty():
            if timeout and (time.time() - start_time) > timeout:
                self.logger.warning("等待任务完成超时")
                break
            
            time.sleep(0.1)
        
        self.logger.info("所有任务已完成")
    
    def get_statistics(self) -> dict:
        """
        获取任务执行统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "total_tasks": self.total_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "running_tasks": len(self.running_tasks),
            "pending_tasks": self.task_queue.qsize(),
            "success_rate": (self.completed_tasks / self.total_tasks * 100) 
                          if self.total_tasks > 0 else 0
        }
    
    def print_statistics(self):
        """打印任务执行统计信息"""
        stats = self.get_statistics()
        self.logger.info("=== 任务执行统计 ===")
        self.logger.info(f"总任务数: {stats['total_tasks']}")
        self.logger.info(f"已完成: {stats['completed_tasks']}")
        self.logger.info(f"失败: {stats['failed_tasks']}")
        self.logger.info(f"运行中: {stats['running_tasks']}")
        self.logger.info(f"等待中: {stats['pending_tasks']}")
        self.logger.info(f"成功率: {stats['success_rate']:.1f}%")


# 示例使用
if __name__ == "__main__":
    def sample_task(name: str, duration: int):
        """示例任务函数"""
        print(f"执行任务: {name}")
        time.sleep(duration)
        return f"任务 {name} 完成"
    
    # 创建任务管理器
    manager = TaskManager(max_workers=2)
    
    # 添加任务
    manager.add_task("task1", "测试任务1", sample_task, "任务1", 2, priority=1)
    manager.add_task("task2", "测试任务2", sample_task, "任务2", 1, priority=2)
    manager.add_task("task3", "测试任务3", sample_task, "任务3", 3, priority=0)
    
    # 启动任务管理器
    manager.start()
    
    # 等待任务完成
    manager.wait_for_completion()
    
    # 打印统计信息
    manager.print_statistics()
    
    # 停止任务管理器
    manager.stop()
