"""
UI自动化测试框架核心类
提供鼠标、键盘操作，图像识别，任务管理等核心功能
"""

import time
import pyautogui
from typing import Optional, Tuple, Callable, Any
from enum import Enum

from .logger import get_logger
from .image_matcher import ImageMatcher, MatchResult
from .task_manager import TaskManager
from ..utils.exceptions import (
    ExceptionHandler, retry_on_exception, safe_execute,
    ImageNotFoundException, ClickFailedException, TypeTextFailedException
)


class ClickType(Enum):
    """点击类型枚举"""
    SINGLE = "single"
    DOUBLE = "double"
    RIGHT = "right"


class ClickPosition(Enum):
    """点击位置枚举"""
    CENTER = "center"
    TOP_LEFT = "top_left"
    TOP_RIGHT = "top_right"
    BOTTOM_LEFT = "bottom_left"
    BOTTOM_RIGHT = "bottom_right"


class UIAutomationFramework:
    """UI自动化测试框架主类"""
    
    def __init__(self, default_confidence=0.8, default_timeout=10, max_workers=3):
        """
        初始化自动化框架

        Args:
            default_confidence: 默认图像识别置信度
            default_timeout: 默认超时时间
            max_workers: 最大工作线程数
        """
        self.logger = get_logger("UIAutomationFramework")
        self.image_matcher = ImageMatcher(default_confidence)
        self.task_manager = TaskManager(max_workers)
        self.exception_handler = ExceptionHandler(self.logger)
        self.default_timeout = default_timeout

        # 配置PyAutoGUI
        pyautogui.FAILSAFE = True  # 启用安全模式
        pyautogui.PAUSE = 0.5      # 操作间隔

        self.logger.info("UI自动化框架初始化完成")
    
    # ==================== 鼠标操作方法 ====================
    
    @retry_on_exception(max_retries=2, delay=0.5, exceptions=[Exception])
    def click(self, x: int, y: int, click_type: ClickType = ClickType.SINGLE,
              duration: float = 0.5) -> bool:
        """
        在指定坐标执行鼠标点击

        Args:
            x: X坐标
            y: Y坐标
            click_type: 点击类型
            duration: 移动持续时间

        Returns:
            操作是否成功
        """
        try:
            self.logger.log_action(f"点击坐标 ({x}, {y}), 类型: {click_type.value}")

            if click_type == ClickType.SINGLE:
                pyautogui.click(x, y, duration=duration)
            elif click_type == ClickType.DOUBLE:
                pyautogui.doubleClick(x, y, duration=duration)
            elif click_type == ClickType.RIGHT:
                pyautogui.rightClick(x, y, duration=duration)

            self.logger.log_result(f"成功点击坐标 ({x}, {y})")
            return True

        except Exception as e:
            click_exception = ClickFailedException(x, y, str(e))
            self.exception_handler.handle_exception(click_exception, "鼠标点击")
            raise click_exception
    
    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, 
             duration: float = 1.0) -> bool:
        """
        拖拽操作
        
        Args:
            start_x: 起始X坐标
            start_y: 起始Y坐标
            end_x: 结束X坐标
            end_y: 结束Y坐标
            duration: 拖拽持续时间
            
        Returns:
            操作是否成功
        """
        try:
            self.logger.log_action(f"拖拽从 ({start_x}, {start_y}) 到 ({end_x}, {end_y})")
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration)
            self.logger.log_result("拖拽操作完成")
            return True
            
        except Exception as e:
            self.logger.error(f"拖拽操作失败: {e}")
            return False
    
    def scroll(self, x: int, y: int, clicks: int) -> bool:
        """
        滚轮操作
        
        Args:
            x: X坐标
            y: Y坐标
            clicks: 滚动次数（正数向上，负数向下）
            
        Returns:
            操作是否成功
        """
        try:
            self.logger.log_action(f"在 ({x}, {y}) 滚动 {clicks} 次")
            pyautogui.scroll(clicks, x=x, y=y)
            self.logger.log_result("滚动操作完成")
            return True
            
        except Exception as e:
            self.logger.error(f"滚动操作失败: {e}")
            return False
    
    # ==================== 键盘操作方法 ====================
    
    def type_text(self, text: str, interval: float = 0.05) -> bool:
        """
        输入文本
        
        Args:
            text: 要输入的文本
            interval: 字符间隔时间
            
        Returns:
            操作是否成功
        """
        try:
            self.logger.log_action(f"输入文本: {text}")
            pyautogui.typewrite(text, interval=interval)
            self.logger.log_result("文本输入完成")
            return True
            
        except Exception as e:
            self.logger.error(f"文本输入失败: {e}")
            return False
    
    def press_key(self, key: str) -> bool:
        """
        按下单个按键
        
        Args:
            key: 按键名称
            
        Returns:
            操作是否成功
        """
        try:
            self.logger.log_action(f"按下按键: {key}")
            pyautogui.press(key)
            self.logger.log_result(f"按键 {key} 操作完成")
            return True
            
        except Exception as e:
            self.logger.error(f"按键操作失败: {e}")
            return False
    
    def press_hotkey(self, *keys) -> bool:
        """
        按下组合键
        
        Args:
            keys: 按键组合
            
        Returns:
            操作是否成功
        """
        try:
            key_combination = "+".join(keys)
            self.logger.log_action(f"按下组合键: {key_combination}")
            pyautogui.hotkey(*keys)
            self.logger.log_result(f"组合键 {key_combination} 操作完成")
            return True
            
        except Exception as e:
            self.logger.error(f"组合键操作失败: {e}")
            return False
    
    # ==================== 图像识别和点击方法 ====================
    
    def find_and_click_image(self, image_path: str, confidence: float = None,
                           click_type: ClickType = ClickType.SINGLE,
                           click_position: ClickPosition = ClickPosition.CENTER,
                           timeout: int = None, retry_interval: float = 1.0) -> bool:
        """
        查找图像并点击
        
        Args:
            image_path: 图像路径
            confidence: 置信度
            click_type: 点击类型
            click_position: 点击位置
            timeout: 超时时间
            retry_interval: 重试间隔
            
        Returns:
            操作是否成功
        """
        if timeout is None:
            timeout = self.default_timeout
        
        self.logger.log_step(f"查找并点击图像: {image_path}")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            # 查找图像
            result = self.image_matcher.find_image_on_screen(
                image_path, confidence, timeout=1
            )
            
            if result and result.found:
                # 计算点击坐标
                click_x, click_y = self._calculate_click_position(result, click_position)
                
                # 执行点击
                if self.click(click_x, click_y, click_type):
                    self.logger.log_result(f"成功点击图像: {image_path}")
                    return True
                else:
                    return False
            
            self.logger.debug(f"未找到图像，{retry_interval}秒后重试...")
            time.sleep(retry_interval)
        
        self.logger.warning(f"查找图像超时: {image_path}")
        return False
    
    def _calculate_click_position(self, result: MatchResult, 
                                position: ClickPosition) -> Tuple[int, int]:
        """
        根据匹配结果和位置类型计算点击坐标
        
        Args:
            result: 匹配结果
            position: 点击位置类型
            
        Returns:
            点击坐标 (x, y)
        """
        if position == ClickPosition.CENTER:
            return result.center_x, result.center_y
        elif position == ClickPosition.TOP_LEFT:
            return result.top_left_x, result.top_left_y
        elif position == ClickPosition.TOP_RIGHT:
            return result.top_left_x + result.width, result.top_left_y
        elif position == ClickPosition.BOTTOM_LEFT:
            return result.top_left_x, result.top_left_y + result.height
        elif position == ClickPosition.BOTTOM_RIGHT:
            return result.top_left_x + result.width, result.top_left_y + result.height
        else:
            return result.center_x, result.center_y
    
    def wait_for_image(self, image_path: str, confidence: float = None,
                      timeout: int = None) -> bool:
        """
        等待图像出现
        
        Args:
            image_path: 图像路径
            confidence: 置信度
            timeout: 超时时间
            
        Returns:
            是否找到图像
        """
        if timeout is None:
            timeout = self.default_timeout
        
        self.logger.log_step(f"等待图像出现: {image_path}")
        return self.image_matcher.wait_for_image(image_path, confidence, timeout)
    
    # ==================== 复合操作方法 ====================
    
    def find_click_and_type(self, image_path: str, text: str,
                          confidence: float = None,
                          click_type: ClickType = ClickType.DOUBLE,
                          click_position: ClickPosition = ClickPosition.CENTER,
                          wait_after_click: float = 1.0) -> bool:
        """
        查找图像、点击并输入文本的复合操作
        
        Args:
            image_path: 图像路径
            text: 要输入的文本
            confidence: 置信度
            click_type: 点击类型
            click_position: 点击位置
            wait_after_click: 点击后等待时间
            
        Returns:
            操作是否成功
        """
        self.logger.log_step(f"执行点击并输入操作: {image_path} -> {text}")
        
        # 查找并点击图像
        if self.find_and_click_image(image_path, confidence, click_type, click_position):
            # 等待界面响应
            time.sleep(wait_after_click)
            
            # 输入文本
            if self.type_text(text):
                self.logger.log_result("点击并输入操作完成")
                return True
        
        self.logger.error("点击并输入操作失败")
        return False
    
    # ==================== 工具方法 ====================
    
    def sleep(self, seconds: float):
        """
        等待指定时间
        
        Args:
            seconds: 等待秒数
        """
        self.logger.log_action(f"等待 {seconds} 秒")
        time.sleep(seconds)
    
    def take_screenshot(self, filename: str = None) -> str:
        """
        截取屏幕截图
        
        Args:
            filename: 保存文件名
            
        Returns:
            截图文件路径
        """
        if filename is None:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
        
        self.image_matcher.save_screenshot(filename)
        return filename
    
    def get_screen_size(self) -> Tuple[int, int]:
        """
        获取屏幕尺寸

        Returns:
            屏幕尺寸 (width, height)
        """
        return pyautogui.size()

    def move_mouse(self, x: int, y: int, duration: float = 0.5) -> bool:
        """
        移动鼠标到指定位置

        Args:
            x: X坐标
            y: Y坐标
            duration: 移动持续时间

        Returns:
            操作是否成功
        """
        try:
            self.logger.log_action(f"移动鼠标到 ({x}, {y})")
            pyautogui.moveTo(x, y, duration=duration)
            self.logger.log_result(f"鼠标已移动到 ({x}, {y})")
            return True
        except Exception as e:
            self.logger.error(f"鼠标移动失败: {e}")
            return False

    def get_mouse_position(self) -> Tuple[int, int]:
        """
        获取当前鼠标位置

        Returns:
            鼠标位置 (x, y)
        """
        return pyautogui.position()

    # ==================== 任务管理方法 ====================

    def add_task(self, task_id: str, name: str, func: Callable,
                *args, priority: int = 0, **kwargs):
        """
        添加任务到执行队列

        Args:
            task_id: 任务唯一标识
            name: 任务名称
            func: 要执行的函数
            args: 函数参数
            priority: 优先级
            kwargs: 函数关键字参数
        """
        return self.task_manager.add_task(task_id, name, func, *args,
                                        priority=priority, **kwargs)

    def start_tasks(self):
        """启动任务执行"""
        self.task_manager.start()
        self.logger.info("任务执行已启动")

    def stop_tasks(self, wait_for_completion: bool = True):
        """
        停止任务执行

        Args:
            wait_for_completion: 是否等待当前任务完成
        """
        self.task_manager.stop(wait_for_completion)
        self.logger.info("任务执行已停止")

    def wait_for_all_tasks(self, timeout: Optional[float] = None):
        """
        等待所有任务完成

        Args:
            timeout: 超时时间（秒）
        """
        self.task_manager.wait_for_completion(timeout)

    def get_task_statistics(self) -> dict:
        """获取任务执行统计信息"""
        return self.task_manager.get_statistics()

    def print_task_statistics(self):
        """打印任务执行统计信息"""
        self.task_manager.print_statistics()


# 示例使用
if __name__ == "__main__":
    # 创建自动化框架实例
    automation = UIAutomationFramework()

    # 获取屏幕尺寸
    width, height = automation.get_screen_size()
    print(f"屏幕尺寸: {width} x {height}")

    # 获取鼠标位置
    mouse_x, mouse_y = automation.get_mouse_position()
    print(f"当前鼠标位置: ({mouse_x}, {mouse_y})")

    # 截取屏幕截图
    screenshot_path = automation.take_screenshot()
    print(f"截图已保存: {screenshot_path}")
