[32m[2025-07-02 18:19:09][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:19:14][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:19:14][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:19:14][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:19:14][INFO][UIAutomation] Started 1 worker threads[0m
[31m[2025-07-02 18:19:14][ERRO][UIAutomation] Image recognition failed: Failed to read D:/python_code/pc_ui_auto_svncode/resource/picture\ljw.png because file is missing, has improper permissions, or is an unsupported or invalid format[0m
[33m[2025-07-02 18:20:01][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 18:20:02][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 18:24:59][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:25:04][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:25:04][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:25:04][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:25:04][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 18:25:06][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 18:25:08][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-07-02 18:25:08][ERRO][UIAutomation] Image recognition failed: Failed to read D:/python_code/pc_ui_auto_svncode/resource/picture\bluetooth.png because file is missing, has improper permissions, or is an unsupported or invalid format[0m
[33m[2025-07-02 18:25:24][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 18:25:25][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 18:25:26][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 18:25:27][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 18:28:05][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:28:10][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:28:10][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:28:10][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:28:10][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 18:28:12][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 18:28:14][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-07-02 18:28:14][ERRO][UIAutomation] Image recognition failed: Failed to read D:/linyu/autotest/resource/resource\bluetooth.png because file is missing, has improper permissions, or is an unsupported or invalid format[0m
[33m[2025-07-02 18:28:22][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 18:28:23][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 18:28:41][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:28:46][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:28:46][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:28:46][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:28:46][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 18:28:48][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 18:28:50][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-07-02 18:28:50][ERRO][UIAutomation] Image recognition failed: Failed to read D:/linyu/autotest/resource/bluetooth.png because file is missing, has improper permissions, or is an unsupported or invalid format[0m
[33m[2025-07-02 18:29:37][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 18:29:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 18:29:40][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:29:45][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:29:45][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:29:45][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:29:45][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 18:29:47][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 18:29:49][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-07-02 18:29:49][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 18:30:08][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 18:30:19][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 18:30:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 18:30:21][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 18:31:59][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:32:04][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:32:04][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:32:04][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:32:04][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 18:32:06][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 18:32:08][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-07-02 18:32:08][ERRO][UIAutomation] Error clicking at position (851, 313): PyAutoGUI was unable to import pytweening. Please install this module to enable the function you tried to call.[0m
[32m[2025-07-02 18:32:08][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-07-02 18:32:08][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-07-02 18:32:09][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 18:32:09][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 18:32:09][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-02 18:35:25][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:35:30][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:35:30][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:35:30][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:35:30][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 18:35:32][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 18:35:34][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-07-02 18:35:35][INFO][UIAutomation] Clicked at position (850, 285)[0m
[32m[2025-07-02 18:35:35][INFO][UIAutomation] Clicked center at (850, 285)[0m
[32m[2025-07-02 18:35:35][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-07-02 18:35:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 18:35:36][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 18:35:36][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-02 18:44:24][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:44:29][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:44:29][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:44:29][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:44:29][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 18:44:31][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 18:44:33][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-07-02 18:44:34][INFO][UIAutomation] Clicked at position (850, 285)[0m
[32m[2025-07-02 18:44:34][INFO][UIAutomation] Clicked center at (850, 285)[0m
[32m[2025-07-02 18:44:36][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-07-02 18:44:37][INFO][UIAutomation] Clicked at position (621, 311)[0m
[32m[2025-07-02 18:44:37][INFO][UIAutomation] Clicked center at (621, 311)[0m
[32m[2025-07-02 18:44:39][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-07-02 18:44:40][INFO][UIAutomation] Clicked at position (927, 396)[0m
[32m[2025-07-02 18:44:40][INFO][UIAutomation] Clicked center at (927, 396)[0m
[32m[2025-07-02 18:44:40][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-07-02 18:44:42][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-02 18:44:42][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 18:44:42][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-02 18:51:30][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 18:51:35][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 18:51:35][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 18:51:35][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 18:51:35][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 18:51:37][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 18:51:39][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-07-02 18:51:40][INFO][UIAutomation] Clicked at position (850, 285)[0m
[32m[2025-07-02 18:51:40][INFO][UIAutomation] Clicked center at (850, 285)[0m
[32m[2025-07-02 18:51:42][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-07-02 18:51:43][INFO][UIAutomation] Clicked at position (621, 311)[0m
[32m[2025-07-02 18:51:43][INFO][UIAutomation] Clicked center at (621, 311)[0m
[32m[2025-07-02 18:51:45][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-07-02 18:51:46][INFO][UIAutomation] Clicked at position (927, 396)[0m
[32m[2025-07-02 18:51:46][INFO][UIAutomation] Clicked center at (927, 396)[0m
[32m[2025-07-02 18:51:46][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-07-02 18:51:50][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-07-02 18:51:51][INFO][UIAutomation] Clicked at position (620, 333)[0m
[32m[2025-07-02 18:51:51][INFO][UIAutomation] Clicked center at (620, 333)[0m
[32m[2025-07-02 18:51:53][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-07-02 18:51:54][INFO][UIAutomation] Clicked at position (852, 494)[0m
[32m[2025-07-02 18:51:54][INFO][UIAutomation] Clicked center at (852, 494)[0m
[32m[2025-07-02 18:51:56][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-07-02 18:51:58][INFO][UIAutomation] Clicked at position (1197, 335)[0m
[32m[2025-07-02 18:51:58][INFO][UIAutomation] Clicked center at (1197, 335)[0m
[32m[2025-07-02 18:52:00][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-07-02 18:52:01][INFO][UIAutomation] Clicked at position (1374, 697)[0m
[32m[2025-07-02 18:52:01][INFO][UIAutomation] Clicked center at (1374, 697)[0m
[32m[2025-07-02 18:52:03][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-07-02 18:52:04][INFO][UIAutomation] Clicked at position (1357, 697)[0m
[32m[2025-07-02 18:52:04][INFO][UIAutomation] Clicked center at (1357, 697)[0m
[32m[2025-07-02 18:52:06][INFO][UIAutomation] 单击ok[0m
[32m[2025-07-02 18:52:07][INFO][UIAutomation] Clicked at position (1328, 863)[0m
[32m[2025-07-02 18:52:07][INFO][UIAutomation] Clicked center at (1328, 863)[0m
[32m[2025-07-02 18:52:09][INFO][UIAutomation] 单击Connect[0m
[32m[2025-07-02 18:52:10][INFO][UIAutomation] Clicked at position (606, 353)[0m
[32m[2025-07-02 18:52:10][INFO][UIAutomation] Clicked center at (606, 353)[0m
[32m[2025-07-02 18:52:20][INFO][UIAutomation] 单击Ring[0m
[31m[2025-07-02 18:52:20][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 18:52:25][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 18:52:26][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 21:56:12][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 21:56:17][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 21:56:17][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 21:56:17][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 21:56:17][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 21:56:19][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 21:56:21][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[31m[2025-07-02 21:56:21][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 21:56:28][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 21:56:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 21:56:47][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 21:56:52][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 21:56:52][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 21:56:52][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 21:56:52][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 21:56:54][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 21:56:56][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-07-02 21:56:57][INFO][UIAutomation] Clicked at position (850, 285)[0m
[32m[2025-07-02 21:56:57][INFO][UIAutomation] Clicked center at (850, 285)[0m
[32m[2025-07-02 21:56:59][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-07-02 21:57:00][INFO][UIAutomation] Clicked at position (493, 311)[0m
[32m[2025-07-02 21:57:00][INFO][UIAutomation] Clicked center at (493, 311)[0m
[32m[2025-07-02 21:57:02][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-07-02 21:57:03][INFO][UIAutomation] Clicked at position (799, 396)[0m
[32m[2025-07-02 21:57:03][INFO][UIAutomation] Clicked center at (799, 396)[0m
[32m[2025-07-02 21:57:03][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-07-02 21:57:07][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-07-02 21:57:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 21:58:59][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 21:59:04][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 21:59:04][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 21:59:04][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-02 21:59:04][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 21:59:06][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-02 21:59:08][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-07-02 21:59:09][INFO][UIAutomation] Clicked at position (850, 285)[0m
[32m[2025-07-02 21:59:09][INFO][UIAutomation] Clicked center at (850, 285)[0m
[32m[2025-07-02 21:59:11][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-07-02 21:59:12][INFO][UIAutomation] Clicked at position (493, 311)[0m
[32m[2025-07-02 21:59:12][INFO][UIAutomation] Clicked center at (493, 311)[0m
[32m[2025-07-02 21:59:14][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-07-02 21:59:15][INFO][UIAutomation] Clicked at position (799, 396)[0m
[32m[2025-07-02 21:59:15][INFO][UIAutomation] Clicked center at (799, 396)[0m
[32m[2025-07-02 21:59:15][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-07-02 21:59:19][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-07-02 21:59:20][INFO][UIAutomation] Clicked at position (492, 333)[0m
[32m[2025-07-02 21:59:20][INFO][UIAutomation] Clicked center at (492, 333)[0m
[32m[2025-07-02 21:59:22][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-07-02 21:59:23][INFO][UIAutomation] Clicked at position (724, 494)[0m
[32m[2025-07-02 21:59:23][INFO][UIAutomation] Clicked center at (724, 494)[0m
[32m[2025-07-02 21:59:25][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-07-02 21:59:27][INFO][UIAutomation] Clicked at position (1069, 335)[0m
[32m[2025-07-02 21:59:27][INFO][UIAutomation] Clicked center at (1069, 335)[0m
[32m[2025-07-02 21:59:29][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-07-02 21:59:30][INFO][UIAutomation] Clicked at position (1246, 697)[0m
[32m[2025-07-02 21:59:30][INFO][UIAutomation] Clicked center at (1246, 697)[0m
[32m[2025-07-02 21:59:32][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-07-02 21:59:33][INFO][UIAutomation] Clicked at position (1229, 697)[0m
[32m[2025-07-02 21:59:33][INFO][UIAutomation] Clicked center at (1229, 697)[0m
[32m[2025-07-02 21:59:35][INFO][UIAutomation] 单击ok[0m
[32m[2025-07-02 21:59:36][INFO][UIAutomation] Clicked at position (1200, 863)[0m
[32m[2025-07-02 21:59:36][INFO][UIAutomation] Clicked center at (1200, 863)[0m
[32m[2025-07-02 21:59:38][INFO][UIAutomation] 单击Connect[0m
[32m[2025-07-02 21:59:39][INFO][UIAutomation] Clicked at position (478, 353)[0m
[32m[2025-07-02 21:59:39][INFO][UIAutomation] Clicked center at (478, 353)[0m
[32m[2025-07-02 21:59:49][INFO][UIAutomation] 单击Ring[0m
[32m[2025-07-02 21:59:50][INFO][UIAutomation] Clicked at position (1037, 355)[0m
[32m[2025-07-02 21:59:50][INFO][UIAutomation] Clicked center at (1037, 355)[0m
[32m[2025-07-02 21:59:54][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-07-02 21:59:55][INFO][UIAutomation] Clicked at position (733, 509)[0m
[32m[2025-07-02 21:59:55][INFO][UIAutomation] Clicked top-right at (733, 509)[0m
[32m[2025-07-02 22:00:03][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-07-02 22:00:08][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-07-02 22:00:09][INFO][UIAutomation] Clicked at position (1148, 355)[0m
[32m[2025-07-02 22:00:09][INFO][UIAutomation] Clicked center at (1148, 355)[0m
[32m[2025-07-02 22:00:09][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-07-02 22:00:17][INFO][UIAutomation] 右键勾选用例[0m
[31m[2025-07-02 22:00:17][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 22:00:35][INFO][UIAutomation] rightClick at position (278, 300)[0m
[32m[2025-07-02 22:00:35][INFO][UIAutomation] Clicked center at (278, 300)[0m
[32m[2025-07-02 22:00:35][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-02 22:00:37][INFO][UIAutomation] 勾选SMD_collections_path[0m
[32m[2025-07-02 22:00:38][INFO][UIAutomation] Clicked at position (460, 510)[0m
[32m[2025-07-02 22:00:38][INFO][UIAutomation] Clicked center at (460, 510)[0m
[32m[2025-07-02 22:00:38][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-02 22:00:40][INFO][UIAutomation] 勾选Marks_from_path[0m
[32m[2025-07-02 22:00:41][INFO][UIAutomation] Clicked at position (769, 582)[0m
[32m[2025-07-02 22:00:41][INFO][UIAutomation] Clicked center at (769, 582)[0m
[32m[2025-07-02 22:00:41][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-02 22:00:43][INFO][UIAutomation] 勾选WB_MAX[0m
[32m[2025-07-02 22:00:45][INFO][UIAutomation] Clicked at position (991, 678)[0m
[32m[2025-07-02 22:00:45][INFO][UIAutomation] Clicked center at (991, 678)[0m
[32m[2025-07-02 22:00:45][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-02 22:00:50][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-07-02 22:00:52][INFO][UIAutomation] 单击Define_ok[0m
[31m[2025-07-02 22:00:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 22:03:47][INFO][UIAutomation] Clicked at position (407, 606)[0m
[32m[2025-07-02 22:03:47][INFO][UIAutomation] Clicked center at (407, 606)[0m
[31m[2025-07-02 22:03:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 22:05:16][INFO][UIAutomation] Clicked at position (555, 354)[0m
[32m[2025-07-02 22:05:16][INFO][UIAutomation] Clicked top-right at (555, 354)[0m
[32m[2025-07-02 22:05:16][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:05:18][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:05:20][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:05:21][INFO][UIAutomation] Clicked at position (552, 377)[0m
[32m[2025-07-02 22:05:21][INFO][UIAutomation] Clicked top-right at (552, 377)[0m
[32m[2025-07-02 22:05:21][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:05:23][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:05:28][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[31m[2025-07-02 22:05:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 22:09:40][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:09:45][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:09:45][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:09:45][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:09:50][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-07-02 22:09:57][INFO][UIAutomation] 右键勾选用例[0m
[32m[2025-07-02 22:09:58][INFO][UIAutomation] rightClick at position (278, 300)[0m
[32m[2025-07-02 22:09:58][INFO][UIAutomation] Clicked center at (278, 300)[0m
[32m[2025-07-02 22:09:58][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-02 22:10:00][INFO][UIAutomation] 勾选SMD_collections_path[0m
[32m[2025-07-02 22:10:01][INFO][UIAutomation] Clicked at position (460, 510)[0m
[32m[2025-07-02 22:10:01][INFO][UIAutomation] Clicked center at (460, 510)[0m
[32m[2025-07-02 22:10:01][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-02 22:10:03][INFO][UIAutomation] 勾选Marks_from_path[0m
[32m[2025-07-02 22:10:04][INFO][UIAutomation] Clicked at position (769, 582)[0m
[32m[2025-07-02 22:10:04][INFO][UIAutomation] Clicked center at (769, 582)[0m
[32m[2025-07-02 22:10:04][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-02 22:10:06][INFO][UIAutomation] 勾选WB_MAX[0m
[32m[2025-07-02 22:10:07][INFO][UIAutomation] Clicked at position (991, 678)[0m
[32m[2025-07-02 22:10:07][INFO][UIAutomation] Clicked center at (991, 678)[0m
[32m[2025-07-02 22:10:07][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-02 22:10:12][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-07-02 22:10:14][INFO][UIAutomation] 单击Define_ok[0m
[32m[2025-07-02 22:10:16][INFO][UIAutomation] Clicked at position (130, 406)[0m
[32m[2025-07-02 22:10:16][INFO][UIAutomation] Clicked center at (130, 406)[0m
[31m[2025-07-02 22:10:16][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 22:10:51][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 22:11:16][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 22:11:26][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 22:11:44][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:11:47][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:11:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:11:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:14:02][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:14:07][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:14:07][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:14:07][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:14:08][INFO][UIAutomation] Clicked at position (1246, 391)[0m
[32m[2025-07-02 22:14:08][INFO][UIAutomation] Clicked top-right at (1246, 391)[0m
[32m[2025-07-02 22:14:08][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:14:10][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:14:12][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:14:13][INFO][UIAutomation] Clicked at position (1228, 414)[0m
[32m[2025-07-02 22:14:13][INFO][UIAutomation] Clicked top-right at (1228, 414)[0m
[32m[2025-07-02 22:14:13][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:14:15][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:14:20][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[31m[2025-07-02 22:14:20][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:14:26][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:14:27][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:14:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:17:16][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:17:21][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:17:21][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:17:21][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:17:21][INFO][UIAutomation] Clicked at position (1246, 391)[0m
[32m[2025-07-02 22:17:21][INFO][UIAutomation] Clicked top-right at (1246, 391)[0m
[32m[2025-07-02 22:17:21][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:17:23][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:17:25][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:17:25][INFO][UIAutomation] Clicked at position (1228, 414)[0m
[32m[2025-07-02 22:17:25][INFO][UIAutomation] Clicked top-right at (1228, 414)[0m
[32m[2025-07-02 22:17:25][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:17:27][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:17:32][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[31m[2025-07-02 22:17:33][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:17:40][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:17:41][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:17:42][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:21:28][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:21:33][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:21:33][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:21:33][INFO][UIAutomation] Started 1 worker threads[0m
[31m[2025-07-02 22:21:33][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:21:36][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[32m[2025-07-02 22:21:47][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:21:52][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:21:52][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:21:52][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:21:52][INFO][UIAutomation] Clicked at position (1246, 399.5)[0m
[32m[2025-07-02 22:21:52][INFO][UIAutomation] Clicked top-right at (1246, 399.5)[0m
[32m[2025-07-02 22:21:52][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:21:54][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:21:56][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:21:56][INFO][UIAutomation] Clicked at position (1228, 422.0)[0m
[32m[2025-07-02 22:21:56][INFO][UIAutomation] Clicked top-right at (1228, 422.0)[0m
[32m[2025-07-02 22:21:56][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:21:58][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:22:03][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[31m[2025-07-02 22:22:04][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:22:12][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[32m[2025-07-02 22:22:55][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:23:00][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:23:00][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:23:00][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:23:00][INFO][UIAutomation] Clicked at position (1246, 399.5)[0m
[32m[2025-07-02 22:23:00][INFO][UIAutomation] Clicked top-right at (1246, 399.5)[0m
[32m[2025-07-02 22:23:00][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:23:02][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:23:04][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:23:07][INFO][UIAutomation] Clicked at position (1228, 422.0)[0m
[32m[2025-07-02 22:23:07][INFO][UIAutomation] Clicked top-right at (1228, 422.0)[0m
[32m[2025-07-02 22:23:07][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:23:09][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:23:14][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[31m[2025-07-02 22:23:16][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:24:45][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[32m[2025-07-02 22:29:16][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:29:21][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:29:21][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:29:21][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:29:22][INFO][UIAutomation] Clicked at position (1232, 398.5)[0m
[32m[2025-07-02 22:29:22][INFO][UIAutomation] Clicked top-right at (1232, 398.5)[0m
[32m[2025-07-02 22:29:22][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:29:25][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:29:27][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:29:30][INFO][UIAutomation] Clicked at position (1227, 422.0)[0m
[32m[2025-07-02 22:29:30][INFO][UIAutomation] Clicked top-right at (1227, 422.0)[0m
[32m[2025-07-02 22:29:30][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:29:32][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:29:37][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[31m[2025-07-02 22:29:39][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:29:46][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:29:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:31:08][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:31:13][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:31:13][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:31:13][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:31:14][INFO][UIAutomation] Clicked at position (1232, 398.5)[0m
[32m[2025-07-02 22:31:14][INFO][UIAutomation] Clicked top-right at (1232, 398.5)[0m
[32m[2025-07-02 22:31:14][INFO][UIAutomation] 双击D_RCV_NET_path[0m
[32m[2025-07-02 22:31:16][INFO][UIAutomation] Typed text: 16.3[0m
[32m[2025-07-02 22:31:16][INFO][UIAutomation] 成功输入msbc RCV_NET参数[0m
[32m[2025-07-02 22:31:18][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:31:19][INFO][UIAutomation] Clicked at position (1227, 422.0)[0m
[32m[2025-07-02 22:31:19][INFO][UIAutomation] Clicked top-right at (1227, 422.0)[0m
[32m[2025-07-02 22:31:19][INFO][UIAutomation] 双击D_SR_REA_path[0m
[32m[2025-07-02 22:31:21][INFO][UIAutomation] Typed text: 1.1[0m
[32m[2025-07-02 22:31:21][INFO][UIAutomation] 成功输入msbc SR_REA参数[0m
[31m[2025-07-02 22:31:23][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:31:27][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:31:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:34:07][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:34:12][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:34:12][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:34:12][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:34:13][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 22:34:13][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:34:13][INFO][UIAutomation] 双击D_RCV_NET_path[0m
[32m[2025-07-02 22:34:15][INFO][UIAutomation] Typed text: 16.3[0m
[32m[2025-07-02 22:34:15][INFO][UIAutomation] 成功输入msbc RCV_NET参数[0m
[32m[2025-07-02 22:34:17][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:34:18][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 22:34:18][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 22:34:18][INFO][UIAutomation] 双击D_SR_REA_path[0m
[32m[2025-07-02 22:34:20][INFO][UIAutomation] Typed text: 1.1[0m
[32m[2025-07-02 22:34:20][INFO][UIAutomation] 成功输入msbc SR_REA参数[0m
[31m[2025-07-02 22:34:23][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:34:46][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:34:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:37:22][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:37:27][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:37:27][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:37:27][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:37:27][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:37:27][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:37:30][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:37:32][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[33m[2025-07-02 22:37:34][WARN][UIAutomation] Task queue is empty or an error occurred: find_click_and_type() got an unexpected keyword argument 'click_position'[0m
[33m[2025-07-02 22:37:35][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:37][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:39][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:41][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:42][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:43][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:44][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:50][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:37:52][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:38:32][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:38:37][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:38:37][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:38:37][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:38:37][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:38:37][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:38:39][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:38:41][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:38:41][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-02 22:38:41][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 22:38:41][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-02 22:39:07][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:39:12][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:39:12][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:39:12][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:39:12][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:39:12][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:39:14][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:39:16][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:39:16][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-02 22:39:16][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 22:39:16][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-02 22:39:44][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:39:49][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:39:49][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:39:49][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:39:50][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 22:39:50][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:39:50][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:39:52][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:39:54][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:39:54][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-02 22:39:54][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 22:39:54][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-02 22:40:08][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:40:13][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:40:13][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:40:13][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:40:14][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 22:40:14][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:40:14][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:40:16][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:40:18][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:40:21][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 22:40:21][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 22:40:21][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:40:24][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:40:29][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:40:29][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-02 22:40:29][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 22:40:29][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-02 22:41:01][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:41:06][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:41:06][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:41:06][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:41:07][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 22:41:07][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:41:07][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:41:09][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:41:11][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:41:14][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 22:41:14][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 22:41:14][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:41:16][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:41:21][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:41:23][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-02 22:41:24][INFO][UIAutomation] Clicked at position (457, 612)[0m
[32m[2025-07-02 22:41:24][INFO][UIAutomation] Clicked center at (457, 612)[0m
[33m[2025-07-02 22:41:26][WARN][UIAutomation] Task queue is empty or an error occurred: find_click_and_type() got an unexpected keyword argument 'click_position'[0m
[33m[2025-07-02 22:41:27][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:30][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:31][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:32][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:33][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:34][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:35][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:37][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:39][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:41][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:42][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:43][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:44][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:50][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:52][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:56][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:57][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:58][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:41:59][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:00][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:01][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:02][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:03][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:04][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:05][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:06][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:07][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:08][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:09][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:10][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:11][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:42:12][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:44:08][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:44:13][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:44:13][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:44:13][INFO][UIAutomation] Started 1 worker threads[0m
[31m[2025-07-02 22:44:13][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:44:44][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:44:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:51:21][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:51:26][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:51:26][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:51:26][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:51:27][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 22:51:27][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:51:27][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:51:29][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:51:31][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:51:34][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 22:51:34][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 22:51:34][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:51:36][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:51:41][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:51:43][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-02 22:51:44][INFO][UIAutomation] Clicked at position (457, 612)[0m
[32m[2025-07-02 22:51:44][INFO][UIAutomation] Clicked center at (457, 612)[0m
[31m[2025-07-02 22:51:47][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:52:28][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:52:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:53:30][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:53:35][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:53:35][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:53:36][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:53:37][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 22:53:37][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:53:37][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:53:39][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:53:41][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:53:44][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 22:53:44][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 22:53:44][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:53:46][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:53:51][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[31m[2025-07-02 22:53:53][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:53:53][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[32m[2025-07-02 22:54:03][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:54:08][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:54:08][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:54:08][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:54:09][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 22:54:09][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:54:09][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:54:11][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:54:13][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:54:17][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 22:54:17][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 22:54:17][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:54:19][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:54:24][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[31m[2025-07-02 22:54:26][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:55:14][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:55:15][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:55:50][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:55:55][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:55:55][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:55:55][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 22:55:56][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 22:55:56][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 22:55:56][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 22:55:59][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 22:56:01][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:56:04][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 22:56:04][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 22:56:04][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 22:56:06][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 22:56:11][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 22:56:13][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-02 22:56:14][INFO][UIAutomation] Clicked at position (457, 612)[0m
[32m[2025-07-02 22:56:14][INFO][UIAutomation] Clicked center at (457, 612)[0m
[31m[2025-07-02 22:56:16][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:58:08][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:58:09][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:58:10][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-02 22:58:11][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 22:59:26][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 22:59:31][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 22:59:31][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 22:59:31][INFO][UIAutomation] Started 1 worker threads[0m
[31m[2025-07-02 22:59:31][ERRO][UIAutomation] Image recognition failed: [0m
[33m[2025-07-02 22:59:53][WARN][UIAutomation] Task queue is empty or an error occurred: 'utf-8' codec can't decode byte 0xff in position 0: invalid start byte[0m
[33m[2025-07-02 22:59:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-02 23:01:11][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 23:01:16][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 23:01:16][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 23:01:16][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-02 23:01:17][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 23:01:17][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 23:01:17][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 23:01:19][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 23:01:21][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:01:24][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 23:01:24][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 23:01:24][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 23:01:26][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 23:01:31][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:01:33][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-02 23:01:34][INFO][UIAutomation] Clicked at position (460, 612)[0m
[32m[2025-07-02 23:01:34][INFO][UIAutomation] Clicked center at (460, 612)[0m
[31m[2025-07-02 23:01:36][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:37][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:39][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:40][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:41][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:42][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:43][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:44][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:45][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:46][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:01:47][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 23:01:49][INFO][UIAutomation] Clicked at position (251, 480.0)[0m
[32m[2025-07-02 23:01:49][INFO][UIAutomation] Clicked top-right at (251, 480.0)[0m
[32m[2025-07-02 23:01:49][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png[0m
[32m[2025-07-02 23:01:52][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png参数[0m
[32m[2025-07-02 23:01:54][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:01:57][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 23:01:57][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 23:01:57][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 23:01:59][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 23:02:04][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:02:07][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 23:02:07][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 23:02:07][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 23:02:09][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 23:02:14][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:02:14][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-02 23:02:14][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 23:02:14][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-02 23:03:06][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-02 23:03:11][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-02 23:03:11][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-02 23:03:11][INFO][UIAutomation] Started 1 worker threads[0m
[31m[2025-07-02 23:03:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 23:03:14][INFO][UIAutomation] Clicked at position (270, 480.0)[0m
[32m[2025-07-02 23:03:14][INFO][UIAutomation] Clicked top-right at (270, 480.0)[0m
[32m[2025-07-02 23:03:14][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-02 23:03:16][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-02 23:03:18][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:03:21][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 23:03:21][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 23:03:21][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 23:03:23][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 23:03:28][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:03:30][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-02 23:03:31][INFO][UIAutomation] Clicked at position (460, 612)[0m
[32m[2025-07-02 23:03:31][INFO][UIAutomation] Clicked center at (460, 612)[0m
[31m[2025-07-02 23:03:33][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:34][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:35][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:37][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:38][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:39][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:40][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:41][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:42][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:03:43][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 23:03:45][INFO][UIAutomation] Clicked at position (251, 480.0)[0m
[32m[2025-07-02 23:03:45][INFO][UIAutomation] Clicked top-right at (251, 480.0)[0m
[32m[2025-07-02 23:03:45][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png[0m
[32m[2025-07-02 23:03:47][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png参数[0m
[32m[2025-07-02 23:03:49][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:03:53][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 23:03:53][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 23:03:53][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 23:03:55][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 23:04:00][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:04:02][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-02 23:04:03][INFO][UIAutomation] Clicked at position (458, 612)[0m
[32m[2025-07-02 23:04:03][INFO][UIAutomation] Clicked center at (458, 612)[0m
[31m[2025-07-02 23:04:05][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:04:06][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:04:07][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:04:08][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:04:09][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-02 23:04:10][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-02 23:04:12][INFO][UIAutomation] Clicked at position (246, 502.0)[0m
[32m[2025-07-02 23:04:12][INFO][UIAutomation] Clicked top-right at (246, 502.0)[0m
[32m[2025-07-02 23:04:12][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-02 23:04:15][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-02 23:04:20][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-02 23:04:22][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-02 23:04:23][INFO][UIAutomation] Clicked at position (462, 612)[0m
[32m[2025-07-02 23:04:23][INFO][UIAutomation] Clicked center at (462, 612)[0m
[32m[2025-07-02 23:04:23][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-02 23:04:23][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-02 23:04:23][INFO][UIAutomation] UI automation framework finished successfully[0m
