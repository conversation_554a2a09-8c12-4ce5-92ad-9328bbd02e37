[32m[2025-07-23 20:14:15][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 20:14:20][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 20:14:20][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-23 20:14:20][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-23 20:14:24][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-07-23 20:14:25][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-23 20:14:25][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-23 20:14:25][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-23 20:23:58][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 20:24:03][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 20:24:03][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-23 20:24:03][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-23 20:24:06][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-07-23 20:24:07][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-23 20:24:07][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-23 20:24:07][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-23 20:41:49][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 20:41:54][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 20:41:54][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-23 20:41:54][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-07-23 20:41:54][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-23 20:41:56][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-07-23 20:41:58][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-07-23 20:41:59][INFO][UIAutomation] Clicked at position (850, 285)[0m
[32m[2025-07-23 20:41:59][INFO][UIAutomation] Clicked center at (850, 285)[0m
[32m[2025-07-23 20:42:01][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-07-23 20:42:02][INFO][UIAutomation] Clicked at position (998, 345)[0m
[32m[2025-07-23 20:42:02][INFO][UIAutomation] Clicked center at (998, 345)[0m
[32m[2025-07-23 20:42:04][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-07-23 20:42:06][INFO][UIAutomation] Clicked at position (1304, 430)[0m
[32m[2025-07-23 20:42:06][INFO][UIAutomation] Clicked center at (1304, 430)[0m
[32m[2025-07-23 20:42:06][INFO][UIAutomation] 退出关闭连接弹框[0m
[32m[2025-07-23 20:42:10][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-07-23 20:42:11][INFO][UIAutomation] Clicked at position (997, 367)[0m
[32m[2025-07-23 20:42:11][INFO][UIAutomation] Clicked center at (997, 367)[0m
[32m[2025-07-23 20:42:13][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-07-23 20:42:14][INFO][UIAutomation] Clicked at position (1229, 528)[0m
[32m[2025-07-23 20:42:14][INFO][UIAutomation] Clicked center at (1229, 528)[0m
[32m[2025-07-23 20:42:16][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-07-23 20:42:17][INFO][UIAutomation] Clicked at position (1535, 369)[0m
[32m[2025-07-23 20:42:17][INFO][UIAutomation] Clicked center at (1535, 369)[0m
[32m[2025-07-23 20:42:19][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-07-23 20:42:20][INFO][UIAutomation] Clicked at position (1712, 731)[0m
[32m[2025-07-23 20:42:20][INFO][UIAutomation] Clicked center at (1712, 731)[0m
[32m[2025-07-23 20:42:22][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-07-23 20:42:23][INFO][UIAutomation] Clicked at position (1695, 731)[0m
[32m[2025-07-23 20:42:23][INFO][UIAutomation] Clicked center at (1695, 731)[0m
[32m[2025-07-23 20:42:25][INFO][UIAutomation] 单击ok[0m
[32m[2025-07-23 20:42:26][INFO][UIAutomation] Clicked at position (1666, 897)[0m
[32m[2025-07-23 20:42:26][INFO][UIAutomation] Clicked center at (1666, 897)[0m
[32m[2025-07-23 20:42:28][INFO][UIAutomation] 单击Connect[0m
[32m[2025-07-23 20:42:29][INFO][UIAutomation] Clicked at position (983, 387)[0m
[32m[2025-07-23 20:42:29][INFO][UIAutomation] Clicked center at (983, 387)[0m
[32m[2025-07-23 20:42:39][INFO][UIAutomation] 单击Ring[0m
[32m[2025-07-23 20:42:40][INFO][UIAutomation] Clicked at position (1503, 389)[0m
[32m[2025-07-23 20:42:40][INFO][UIAutomation] Clicked center at (1503, 389)[0m
[32m[2025-07-23 20:42:44][INFO][UIAutomation] 选择音量8/15[0m
[32m[2025-07-23 20:42:45][INFO][UIAutomation] Clicked at position (1238, 550.5)[0m
[32m[2025-07-23 20:42:45][INFO][UIAutomation] Clicked top-right at (1238, 550.5)[0m
[32m[2025-07-23 20:42:54][INFO][UIAutomation] Typed text: 15[0m
[32m[2025-07-23 20:42:59][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-07-23 20:43:00][INFO][UIAutomation] Clicked at position (1614, 389)[0m
[32m[2025-07-23 20:43:00][INFO][UIAutomation] Clicked center at (1614, 389)[0m
[32m[2025-07-23 20:43:05][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-07-23 20:43:12][INFO][UIAutomation] 右键勾选用例[0m
[32m[2025-07-23 20:43:13][INFO][UIAutomation] rightClick at position (278, 234)[0m
[32m[2025-07-23 20:43:13][INFO][UIAutomation] Clicked center at (278, 234)[0m
[32m[2025-07-23 20:43:13][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-23 20:43:15][INFO][UIAutomation] 勾选SMD_collections_path[0m
[32m[2025-07-23 20:43:16][INFO][UIAutomation] Clicked at position (460, 444)[0m
[32m[2025-07-23 20:43:16][INFO][UIAutomation] Clicked center at (460, 444)[0m
[32m[2025-07-23 20:43:16][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-23 20:43:18][INFO][UIAutomation] 勾选Marks_from_path[0m
[32m[2025-07-23 20:43:19][INFO][UIAutomation] Clicked at position (769, 516)[0m
[32m[2025-07-23 20:43:19][INFO][UIAutomation] Clicked center at (769, 516)[0m
[32m[2025-07-23 20:43:19][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-23 20:43:21][INFO][UIAutomation] 勾选WB_MAX[0m
[32m[2025-07-23 20:43:23][INFO][UIAutomation] Clicked at position (991, 612)[0m
[32m[2025-07-23 20:43:23][INFO][UIAutomation] Clicked center at (991, 612)[0m
[32m[2025-07-23 20:43:23][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-07-23 20:43:28][INFO][UIAutomation] 快捷方式运行用例[0m
[32m[2025-07-23 20:43:30][INFO][UIAutomation] 单击Define_ok[0m
[32m[2025-07-23 20:43:31][INFO][UIAutomation] Clicked at position (156, 432)[0m
[32m[2025-07-23 20:43:31][INFO][UIAutomation] Clicked center at (156, 432)[0m
[33m[2025-07-23 20:43:31][WARN][UIAutomation] Task queue is empty or an error occurred: find_click_and_type() got an unexpected keyword argument 'click_position'[0m
[33m[2025-07-23 20:43:32][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:33][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:34][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:35][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:37][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:39][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:41][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:42][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:43][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:44][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:50][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:52][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:56][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:57][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:58][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:43:59][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:00][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:01][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:02][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:03][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:04][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:05][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:06][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:07][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:08][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:09][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:10][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:11][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:12][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:13][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:14][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:15][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:16][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:17][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:18][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:19][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:21][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:44:22][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-23 20:49:44][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 20:49:49][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 20:49:49][INFO][UIAutomation] Starting UI automation framework[0m
[33m[2025-07-23 20:49:49][WARN][UIAutomation] Task queue is empty or an error occurred: find_click_and_type() got an unexpected keyword argument 'click_position'[0m
[32m[2025-07-23 20:49:49][INFO][UIAutomation] Started 1 worker threads[0m
[33m[2025-07-23 20:49:50][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:52][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:56][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:57][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:58][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:49:59][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:00][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:01][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:02][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:03][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:04][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:05][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:06][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:07][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:50:08][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-23 20:51:38][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 20:51:43][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 20:51:43][INFO][UIAutomation] Starting UI automation framework[0m
[33m[2025-07-23 20:51:43][WARN][UIAutomation] Task queue is empty or an error occurred: find_click_and_type() got an unexpected keyword argument 'click_time'[0m
[32m[2025-07-23 20:51:43][INFO][UIAutomation] Started 1 worker threads[0m
[33m[2025-07-23 20:51:44][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:50][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:52][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:56][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:57][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:58][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:51:59][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:52:00][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:52:01][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:52:02][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:52:03][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:52:04][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:52:05][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:52:06][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:52:07][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-23 20:54:03][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 20:54:08][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 20:54:08][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-23 20:54:08][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-07-23 20:54:09][INFO][UIAutomation] Clicked at position (865, 529.0)[0m
[32m[2025-07-23 20:54:09][INFO][UIAutomation] Clicked top-right at (865, 529.0)[0m
[32m[2025-07-23 20:54:09][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-23 20:54:12][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-23 20:54:14][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:54:17][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 20:54:17][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 20:54:17][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 20:54:19][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 20:54:24][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:54:26][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-23 20:54:27][INFO][UIAutomation] Clicked at position (1058, 661)[0m
[32m[2025-07-23 20:54:27][INFO][UIAutomation] Clicked center at (1058, 661)[0m
[31m[2025-07-23 20:54:29][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:30][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:31][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:32][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:34][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:35][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:36][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:37][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:38][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:39][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:54:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 20:54:42][INFO][UIAutomation] Clicked at position (846, 529.0)[0m
[32m[2025-07-23 20:54:42][INFO][UIAutomation] Clicked top-right at (846, 529.0)[0m
[32m[2025-07-23 20:54:42][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png[0m
[32m[2025-07-23 20:54:44][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png参数[0m
[32m[2025-07-23 20:54:46][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:54:49][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 20:54:49][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 20:54:49][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 20:54:52][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 20:54:57][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:54:59][INFO][UIAutomation] 单击Edit_correction_ok[0m
[32m[2025-07-23 20:55:00][INFO][UIAutomation] Clicked at position (1056, 661)[0m
[32m[2025-07-23 20:55:00][INFO][UIAutomation] Clicked center at (1056, 661)[0m
[31m[2025-07-23 20:55:02][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:55:03][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:55:04][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:55:05][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:55:06][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:55:07][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 20:55:09][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 20:55:09][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 20:55:09][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 20:55:12][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 20:55:17][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:55:19][INFO][UIAutomation] 单击Edit_correction_ok[0m
[33m[2025-07-23 20:55:19][WARN][UIAutomation] Task queue is empty or an error occurred: find_and_click_image() got an unexpected keyword argument 'click_time1'[0m
[33m[2025-07-23 20:55:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:21][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:22][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:23][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:24][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:25][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:26][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:27][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:30][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:31][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:32][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:33][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:34][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:35][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:37][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:39][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:41][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:42][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:43][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:44][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:50][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:52][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:56][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:57][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:58][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:55:59][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:00][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:01][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:02][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:03][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:04][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:05][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:06][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:07][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:08][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:09][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:10][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:11][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:12][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:13][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:14][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:15][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:16][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:17][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:18][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:19][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:20][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:21][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:22][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:23][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:24][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:25][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:26][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:27][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:30][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:31][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:32][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:33][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:34][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:35][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:37][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:39][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:41][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:42][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:43][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:44][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:45][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:46][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:47][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:48][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:49][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:50][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:52][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:53][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[33m[2025-07-23 20:56:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-07-23 20:58:22][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 20:58:27][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 20:58:27][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-23 20:58:27][INFO][UIAutomation] Started 1 worker threads[0m
[31m[2025-07-23 20:58:27][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:28][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:29][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:30][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:31][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:32][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:33][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:35][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:36][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:37][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:38][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:39][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:58:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 20:58:42][INFO][UIAutomation] Clicked at position (865, 529.0)[0m
[32m[2025-07-23 20:58:42][INFO][UIAutomation] Clicked top-right at (865, 529.0)[0m
[32m[2025-07-23 20:58:42][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-23 20:58:44][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-23 20:58:46][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:58:49][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 20:58:49][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 20:58:49][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 20:58:52][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 20:58:57][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:58:59][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-23 20:59:00][INFO][UIAutomation] Clicked at position (1058, 661)[0m
[32m[2025-07-23 20:59:00][INFO][UIAutomation] Clicked center at (1058, 661)[0m
[31m[2025-07-23 20:59:02][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:03][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:04][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:05][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:06][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:07][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:08][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:10][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:11][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:12][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 20:59:14][INFO][UIAutomation] Clicked at position (846, 529.0)[0m
[32m[2025-07-23 20:59:14][INFO][UIAutomation] Clicked top-right at (846, 529.0)[0m
[32m[2025-07-23 20:59:14][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png[0m
[32m[2025-07-23 20:59:16][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png参数[0m
[32m[2025-07-23 20:59:18][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:59:21][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 20:59:21][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 20:59:21][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 20:59:23][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 20:59:28][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:59:30][INFO][UIAutomation] 单击Edit_correction_ok[0m
[32m[2025-07-23 20:59:31][INFO][UIAutomation] Clicked at position (1056, 661)[0m
[32m[2025-07-23 20:59:31][INFO][UIAutomation] Clicked center at (1056, 661)[0m
[31m[2025-07-23 20:59:33][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:35][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:36][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:37][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:38][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:39][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 20:59:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 20:59:42][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 20:59:42][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 20:59:42][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 20:59:44][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 20:59:49][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 20:59:51][INFO][UIAutomation] 单击Edit_correction_ok[0m
[32m[2025-07-23 20:59:52][INFO][UIAutomation] Clicked at position (1060, 661)[0m
[32m[2025-07-23 20:59:52][INFO][UIAutomation] Clicked center at (1060, 661)[0m
[32m[2025-07-23 20:59:52][INFO][UIAutomation] 等待用例执行完成[0m
[32m[2025-07-23 20:59:52][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-23 20:59:52][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-23 20:59:52][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-07-23 21:09:37][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 21:09:42][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 21:09:42][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-23 21:09:42][INFO][UIAutomation] Started 1 worker threads[0m
[31m[2025-07-23 21:09:43][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:09:44][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:09:45][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:09:46][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:09:47][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:09:48][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:09:49][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:09:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 21:10:06][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-07-23 21:10:11][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-07-23 21:10:11][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-07-23 21:10:11][INFO][UIAutomation] Started 1 worker threads[0m
[31m[2025-07-23 21:10:11][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:12][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 21:10:15][INFO][UIAutomation] Clicked at position (867, 529.0)[0m
[32m[2025-07-23 21:10:15][INFO][UIAutomation] Clicked top-right at (867, 529.0)[0m
[32m[2025-07-23 21:10:15][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png[0m
[32m[2025-07-23 21:10:17][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_RCV_NET.png参数[0m
[32m[2025-07-23 21:10:19][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 21:10:23][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 21:10:23][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 21:10:23][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 21:10:25][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 21:10:30][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 21:10:32][INFO][UIAutomation] 双击Edit_correction_ok[0m
[32m[2025-07-23 21:10:33][INFO][UIAutomation] Clicked at position (1055, 661)[0m
[32m[2025-07-23 21:10:33][INFO][UIAutomation] Clicked center at (1055, 661)[0m
[31m[2025-07-23 21:10:35][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:36][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:37][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:38][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:39][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:40][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:42][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:43][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:44][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:45][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:10:46][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 21:10:48][INFO][UIAutomation] Clicked at position (846, 529.0)[0m
[32m[2025-07-23 21:10:48][INFO][UIAutomation] Clicked top-right at (846, 529.0)[0m
[32m[2025-07-23 21:10:48][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png[0m
[32m[2025-07-23 21:10:50][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SND_NET.png参数[0m
[32m[2025-07-23 21:10:52][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 21:10:55][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 21:10:55][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 21:10:55][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 21:10:57][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 21:11:02][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 21:11:05][INFO][UIAutomation] 单击Edit_correction_ok[0m
[32m[2025-07-23 21:11:06][INFO][UIAutomation] Clicked at position (1054, 661)[0m
[32m[2025-07-23 21:11:06][INFO][UIAutomation] Clicked center at (1054, 661)[0m
[31m[2025-07-23 21:11:08][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:11:09][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:11:10][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:11:11][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:11:12][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:11:13][ERRO][UIAutomation] Image recognition failed: [0m
[31m[2025-07-23 21:11:14][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-07-23 21:11:16][INFO][UIAutomation] Clicked at position (841, 551.0)[0m
[32m[2025-07-23 21:11:16][INFO][UIAutomation] Clicked top-right at (841, 551.0)[0m
[32m[2025-07-23 21:11:16][INFO][UIAutomation] 双击D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png[0m
[32m[2025-07-23 21:11:19][INFO][UIAutomation] 成功输入msbc D:/linyu/autotest/Testcode/resource/picture/D_SR_REA.png参数[0m
[32m[2025-07-23 21:11:24][INFO][UIAutomation] 单击Edit_varrible_ok[0m
[32m[2025-07-23 21:11:26][INFO][UIAutomation] 单击Edit_correction_ok[0m
[32m[2025-07-23 21:11:27][INFO][UIAutomation] Clicked at position (1058, 661)[0m
[32m[2025-07-23 21:11:27][INFO][UIAutomation] Clicked center at (1058, 661)[0m
[32m[2025-07-23 21:11:27][INFO][UIAutomation] 等待用例执行完成[0m
[32m[2025-07-23 21:11:27][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-07-23 21:11:27][INFO][UIAutomation] All threads stopped[0m
[32m[2025-07-23 21:11:27][INFO][UIAutomation] UI automation framework finished successfully[0m
