[32m - 2025-03-27 01:45:55,087 - UIAutomationLogger - INFO - UIAutomationFramework initialized--[0m
[32m - 2025-03-27 01:46:00,089 - UIAutomationLogger - INFO - Added task: test_task1--[0m
[32m - 2025-03-27 01:46:00,089 - UIA<PERSON><PERSON>Logger - INFO - Added task: test_task2--[0m
[32m - 2025-03-27 01:46:00,089 - UIAutomationLogger - INFO - Starting UI automation framework--[0m
[32m - 2025-03-27 01:46:00,131 - UIAutomationLogger - INFO - Started alert handling thread--[0m
[32m - 2025-03-27 01:46:00,132 - UIA<PERSON><PERSON>Logger - INFO - Starting test task 1--[0m
[32m - 2025-03-27 01:46:00,132 - UIAutomationLogger - INFO - Starting test task 2--[0m
[32m - 2025-03-27 01:46:00,132 - UIA<PERSON>mationLogger - INFO - Started 2 worker threads--[0m
[31m - 2025-03-27 01:46:00,168 - U<PERSON><PERSON><PERSON>Logger - ERROR - Error finding and clicking image: Failed to read alert_button.png because file is missing, has improper permissions, or is an unsupported or invalid format--[0m
[32m - 2025-03-27 01:46:00,385 - UIAutomationLogger - INFO - Clicked at position (494, 328)--[0m
[32m - 2025-03-27 01:46:00,385 - UIAutomationLogger - INFO - Found and clicked image at (494, 328)--[0m
[32m - 2025-03-27 01:46:00,385 - UIAutomationLogger - INFO - Completed test task 1--[0m
[32m - 2025-03-27 01:46:00,385 - UIAutomationLogger - INFO - Task completed: test_task1--[0m
[31m - 2025-03-27 01:46:01,216 - UIAutomationLogger - ERROR - Error finding and clicking image: Failed to read alert_button.png because file is missing, has improper permissions, or is an unsupported or invalid format--[0m
[33m - 2025-03-27 01:46:01,387 - UIAutomationLogger - WARNING - Task queue is empty or an error occurred: --[0m
[31m - 2025-03-27 01:46:02,263 - UIAutomationLogger - ERROR - Error finding and clicking image: Failed to read alert_button.png because file is missing, has improper permissions, or is an unsupported or invalid format--[0m
[33m - 2025-03-27 01:46:02,401 - UIAutomationLogger - WARNING - Task queue is empty or an error occurred: --[0m
[31m - 2025-03-27 01:46:03,312 - UIAutomationLogger - ERROR - Error finding and clicking image: Failed to read alert_button.png because file is missing, has improper permissions, or is an unsupported or invalid format--[0m
[33m - 2025-03-27 01:46:03,401 - UIAutomationLogger - WARNING - Task queue is empty or an error occurred: --[0m
[31m - 2025-03-27 01:46:04,363 - UIAutomationLogger - ERROR - Error finding and clicking image: Failed to read alert_button.png because file is missing, has improper permissions, or is an unsupported or invalid format--[0m
[33m - 2025-03-27 01:46:04,401 - UIAutomationLogger - WARNING - Task queue is empty or an error occurred: --[0m
[32m - 2025-03-27 01:46:05,310 - UIAutomationLogger - INFO - Typed text: Hello, World!--[0m
[32m - 2025-03-27 01:46:05,310 - UIAutomationLogger - INFO - Completed test task 2--[0m
[32m - 2025-03-27 01:46:05,310 - UIAutomationLogger - INFO - Task completed: test_task2--[0m
[33m - 2025-03-27 01:46:05,404 - UIAutomationLogger - WARNING - Task queue is empty or an error occurred: --[0m
[32m - 2025-03-27 01:46:05,404 - UIAutomationLogger - INFO - All threads stopped--[0m
[32m - 2025-03-27 01:46:05,404 - UIAutomationLogger - INFO - UI automation framework finished successfully--[0m
