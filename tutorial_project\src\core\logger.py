"""
日志系统模块
提供彩色日志输出、文件轮转、多级别日志记录功能
"""

import logging
import os
import sys
from datetime import datetime
from logging.handlers import RotatingFileHandler
from pathlib import Path
from colorlog import ColoredFormatter


class AutomationLogger:
    """自动化测试框架日志管理器"""
    
    def __init__(self, name="UIAutomation", log_level=logging.INFO):
        """
        初始化日志系统
        
        Args:
            name: 日志器名称
            log_level: 日志级别
        """
        self.name = name
        self.log_level = log_level
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """配置日志系统"""
        # 创建日志器
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(self.log_level)
        
        # 清除已有的处理器
        if self.logger.hasHandlers():
            self.logger.handlers.clear()
        
        # 添加文件处理器
        self._add_file_handler()
        
        # 添加控制台处理器
        self._add_console_handler()
        
        # 禁用第三方库的详细日志
        self._configure_third_party_loggers()
    
    def _add_file_handler(self):
        """添加文件日志处理器"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 按月创建子目录
        month_dir = log_dir / datetime.now().strftime("%Y-%m")
        month_dir.mkdir(exist_ok=True)
        
        # 生成日志文件名
        log_filename = f"automation_{datetime.now().strftime('%Y%m%d')}.log"
        log_file = month_dir / log_filename
        
        # 创建轮转文件处理器
        file_handler = RotatingFileHandler(
            filename=str(log_file),
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        
        # 设置文件日志格式
        file_formatter = logging.Formatter(
            fmt='[%(asctime)s][%(levelname)8s][%(name)s][%(funcName)s:%(lineno)d] %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        
        self.logger.addHandler(file_handler)
    
    def _add_console_handler(self):
        """添加控制台日志处理器"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        
        # 设置彩色控制台格式
        console_formatter = ColoredFormatter(
            fmt='%(log_color)s[%(asctime)s][%(levelname)8s][%(name)s] %(message)s%(reset)s',
            datefmt='%H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        console_handler.setFormatter(console_formatter)
        
        self.logger.addHandler(console_handler)
    
    def _configure_third_party_loggers(self):
        """配置第三方库的日志级别"""
        # 设置第三方库日志级别，避免过多输出
        third_party_loggers = [
            'pyautogui',
            'PIL',
            'opencv',
            'matplotlib',
            'urllib3'
        ]
        
        for logger_name in third_party_loggers:
            logging.getLogger(logger_name).setLevel(logging.WARNING)
    
    def get_logger(self):
        """获取日志器实例"""
        return self.logger
    
    def debug(self, message):
        """记录调试信息"""
        self.logger.debug(message)
    
    def info(self, message):
        """记录一般信息"""
        self.logger.info(message)
    
    def warning(self, message):
        """记录警告信息"""
        self.logger.warning(message)
    
    def error(self, message):
        """记录错误信息"""
        self.logger.error(message)
    
    def critical(self, message):
        """记录严重错误信息"""
        self.logger.critical(message)
    
    def log_test_start(self, test_name):
        """记录测试开始"""
        self.info(f"{'='*50}")
        self.info(f"开始执行测试: {test_name}")
        self.info(f"{'='*50}")
    
    def log_test_end(self, test_name, success=True):
        """记录测试结束"""
        status = "成功" if success else "失败"
        self.info(f"{'='*50}")
        self.info(f"测试完成: {test_name} - {status}")
        self.info(f"{'='*50}")
    
    def log_step(self, step_description):
        """记录测试步骤"""
        self.info(f"[步骤] {step_description}")
    
    def log_action(self, action_description):
        """记录操作动作"""
        self.info(f"[操作] {action_description}")
    
    def log_result(self, result_description):
        """记录结果"""
        self.info(f"[结果] {result_description}")


# 创建全局日志器实例
def get_logger(name="UIAutomation", level=logging.INFO):
    """
    获取日志器实例的便捷函数
    
    Args:
        name: 日志器名称
        level: 日志级别
        
    Returns:
        AutomationLogger实例
    """
    return AutomationLogger(name, level)


# 示例使用
if __name__ == "__main__":
    # 创建日志器
    logger = get_logger("TestLogger")
    
    # 测试各种日志级别
    logger.debug("这是调试信息")
    logger.info("这是一般信息")
    logger.warning("这是警告信息")
    logger.error("这是错误信息")
    logger.critical("这是严重错误信息")
    
    # 测试测试流程日志
    logger.log_test_start("蓝牙连接测试")
    logger.log_step("准备测试环境")
    logger.log_action("点击蓝牙设置按钮")
    logger.log_result("成功打开蓝牙设置界面")
    logger.log_test_end("蓝牙连接测试", success=True)
