"""
自定义异常类和异常处理工具
提供框架专用的异常类型和处理机制
"""

import functools
import time
from typing import Callable, Any, Type, Union, List


class AutomationException(Exception):
    """自动化框架基础异常类"""
    pass


class ImageNotFoundException(AutomationException):
    """图像未找到异常"""
    def __init__(self, image_path: str, confidence: float = None):
        self.image_path = image_path
        self.confidence = confidence
        message = f"未找到图像: {image_path}"
        if confidence:
            message += f" (置信度: {confidence})"
        super().__init__(message)


class ClickFailedException(AutomationException):
    """点击操作失败异常"""
    def __init__(self, x: int, y: int, reason: str = ""):
        self.x = x
        self.y = y
        self.reason = reason
        message = f"点击操作失败: ({x}, {y})"
        if reason:
            message += f" - {reason}"
        super().__init__(message)


class TypeTextFailedException(AutomationException):
    """文本输入失败异常"""
    def __init__(self, text: str, reason: str = ""):
        self.text = text
        self.reason = reason
        message = f"文本输入失败: {text}"
        if reason:
            message += f" - {reason}"
        super().__init__(message)


class TaskTimeoutException(AutomationException):
    """任务超时异常"""
    def __init__(self, task_name: str, timeout: float):
        self.task_name = task_name
        self.timeout = timeout
        message = f"任务超时: {task_name} (超时时间: {timeout}秒)"
        super().__init__(message)


class ConfigurationException(AutomationException):
    """配置错误异常"""
    def __init__(self, config_item: str, reason: str = ""):
        self.config_item = config_item
        self.reason = reason
        message = f"配置错误: {config_item}"
        if reason:
            message += f" - {reason}"
        super().__init__(message)


class ResourceNotFoundException(AutomationException):
    """资源未找到异常"""
    def __init__(self, resource_path: str, resource_type: str = "文件"):
        self.resource_path = resource_path
        self.resource_type = resource_type
        message = f"{resource_type}未找到: {resource_path}"
        super().__init__(message)


def retry_on_exception(max_retries: int = 3, 
                      delay: float = 1.0,
                      backoff_factor: float = 2.0,
                      exceptions: Union[Type[Exception], List[Type[Exception]]] = Exception):
    """
    重试装饰器，在指定异常发生时自动重试
    
    Args:
        max_retries: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff_factor: 延迟时间递增因子
        exceptions: 需要重试的异常类型
    """
    if not isinstance(exceptions, (list, tuple)):
        exceptions = [exceptions]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    last_exception = e
                    if attempt < max_retries:
                        print(f"第 {attempt + 1} 次尝试失败: {e}")
                        print(f"等待 {current_delay:.1f} 秒后重试...")
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        print(f"所有重试都失败了，抛出最后一个异常")
                        raise last_exception
                except Exception as e:
                    # 对于不在重试列表中的异常，直接抛出
                    raise e
            
            # 理论上不会到达这里
            raise last_exception
        
        return wrapper
    return decorator


def timeout_handler(timeout_seconds: float):
    """
    超时处理装饰器
    
    Args:
        timeout_seconds: 超时时间（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            import signal
            
            def timeout_signal_handler(signum, frame):
                raise TaskTimeoutException(func.__name__, timeout_seconds)
            
            # 设置超时信号
            old_handler = signal.signal(signal.SIGALRM, timeout_signal_handler)
            signal.alarm(int(timeout_seconds))
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                # 清除超时信号
                signal.alarm(0)
                signal.signal(signal.SIGALRM, old_handler)
        
        return wrapper
    return decorator


def safe_execute(func: Callable, *args, default_return=None, 
                log_errors: bool = True, **kwargs) -> Any:
    """
    安全执行函数，捕获所有异常并返回默认值
    
    Args:
        func: 要执行的函数
        args: 函数参数
        default_return: 异常时的默认返回值
        log_errors: 是否记录错误日志
        kwargs: 函数关键字参数
        
    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            print(f"函数执行异常: {func.__name__} - {e}")
        return default_return


class ExceptionHandler:
    """异常处理器类"""
    
    def __init__(self, logger=None):
        """
        初始化异常处理器
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger
        self.exception_counts = {}
    
    def handle_exception(self, exception: Exception, context: str = ""):
        """
        处理异常
        
        Args:
            exception: 异常对象
            context: 异常上下文信息
        """
        exception_type = type(exception).__name__
        
        # 统计异常次数
        self.exception_counts[exception_type] = self.exception_counts.get(exception_type, 0) + 1
        
        # 记录异常信息
        error_message = f"异常类型: {exception_type}, 消息: {str(exception)}"
        if context:
            error_message = f"[{context}] {error_message}"
        
        if self.logger:
            self.logger.error(error_message)
        else:
            print(f"ERROR: {error_message}")
        
        # 特殊异常处理
        if isinstance(exception, ImageNotFoundException):
            self._handle_image_not_found(exception)
        elif isinstance(exception, TaskTimeoutException):
            self._handle_task_timeout(exception)
        elif isinstance(exception, ClickFailedException):
            self._handle_click_failed(exception)
    
    def _handle_image_not_found(self, exception: ImageNotFoundException):
        """处理图像未找到异常"""
        if self.logger:
            self.logger.warning(f"建议检查图像文件是否存在: {exception.image_path}")
            self.logger.warning(f"建议降低置信度阈值，当前: {exception.confidence}")
    
    def _handle_task_timeout(self, exception: TaskTimeoutException):
        """处理任务超时异常"""
        if self.logger:
            self.logger.warning(f"任务 {exception.task_name} 执行超时")
            self.logger.warning(f"建议增加超时时间或优化任务逻辑")
    
    def _handle_click_failed(self, exception: ClickFailedException):
        """处理点击失败异常"""
        if self.logger:
            self.logger.warning(f"点击坐标 ({exception.x}, {exception.y}) 失败")
            self.logger.warning("建议检查坐标是否正确或界面是否发生变化")
    
    def get_exception_statistics(self) -> dict:
        """获取异常统计信息"""
        return self.exception_counts.copy()
    
    def reset_statistics(self):
        """重置异常统计"""
        self.exception_counts.clear()


# 示例使用
if __name__ == "__main__":
    # 测试重试装饰器
    @retry_on_exception(max_retries=3, delay=0.5, exceptions=[ValueError])
    def test_function(should_fail: bool = True):
        if should_fail:
            raise ValueError("测试异常")
        return "成功"
    
    # 测试异常处理器
    handler = ExceptionHandler()
    
    try:
        test_function(True)
    except Exception as e:
        handler.handle_exception(e, "测试上下文")
    
    print("异常统计:", handler.get_exception_statistics())
