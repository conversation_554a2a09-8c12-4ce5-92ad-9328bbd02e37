import pyautogui
import time
import threading
from pyautogui import sleep
from queue import Queue
import logging
from logging.handlers import RotatingFileHandler
import os
from datetime import datetime
from colorlog import ColoredFormatter
from script.Framework.human_mouse import human_move_and_click
import sys
import msvcrt  # Windows专用库

# 日志文件路径
LOG_DIR = "D:/linyu/autotest/Testcode/script/logs"
LOG_SUBDIR = datetime.now().strftime("%Y-%m")  # 按月创建子目录
log_dir = os.path.join(LOG_DIR, LOG_SUBDIR)

def setup_logger():
    """优化版日志系统配置"""

    # 创建分层日志目录
    # log_dir = os.path.join(LOG_DIR, LOG_SUBDIR)
    try:
        os.makedirs(log_dir, exist_ok=True)
    except Exception as e:
        raise RuntimeError(f"无法创建日志目录 {log_dir}: {str(e)}")

    # 生成带序列的日志文件名
    log_base = f"automation_{datetime.now().strftime('%d')}"  # 按天生成基准名
    log_file = os.path.join(log_dir, f"{log_base}.log")

    # 初始化日志器
    logger = logging.getLogger("UIAutomation")
    logger.setLevel(logging.DEBUG)

    # 配置文件回滚处理器
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=5 * 1024 * 1024,  # 增大到5MB/文件
        backupCount=5,
        encoding='utf-8',
        delay=True  # 延迟写入直到真正需要
    )
    file_handler.setLevel(logging.DEBUG)

    # 控制台处理器配置
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 增强型日志格式
    log_format = "%(log_color)s[%(asctime)s][%(levelname).4s][%(name)s] %(message)s"
    formatter = ColoredFormatter(
        log_format,
        datefmt="%Y-%m-%d %H:%M:%S",
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white'
        }
    )

    # 应用格式
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 清理旧处理器
    if logger.hasHandlers():
        logger.handlers.clear()

    # 添加处理器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 禁用第三方库日志
    logging.getLogger("pyautogui").setLevel(logging.WARNING)
    logging.getLogger("PIL").setLevel(logging.ERROR)

    return logger

class UIAutomationFramework:
    def __init__(self):
        # 初始化任务队列
        self.task_queue = Queue()
        # 初始化线程列表
        self.threads = []
        # 初始化弹框处理线程
        self.alert_thread = None
        # 用于控制是否继续运行任务
        self.running = True
        # 初始化日志系统
        self.logger = setup_logger()
        self.logger.info("UIAutomationFramework initialized")

    def mouse_singleclick(self, x, y):
        """模拟鼠标点击指定坐标"""
        try:
            # human_move_and_click(x, y)
            pyautogui.click(x, y, duration=0.8)
            #pyautogui.leftClick(x, y)
            self.logger.info(f"Clicked at position ({x}, {y})")
        except Exception as e:
            self.logger.error(f"Error clicking at position ({x}, {y}): {e}")

    def mouse_rightclick(self, x, y):
        """模拟鼠标点击指定坐标"""
        try:
            # human_move_and_click(x, y, button='right')
            pyautogui.rightClick(x, y, duration=0.8)
            #pyautogui.leftClick(x, y)
            self.logger.info(f"rightClick at position ({x}, {y})")
        except Exception as e:
            self.logger.error(f"Error rightClick at position ({x}, {y}): {e}")

    def mouse_doubleclick(self, x, y):
        """模拟鼠标点击指定坐标"""
        try:
            # human_move_and_click(x, y)
            pyautogui.doubleClick(x, y, duration=0.8)
            self.logger.info(f"Clicked at position ({x}, {y})")
        except Exception as e:
            self.logger.error(f"Error clicking at position ({x}, {y}): {e}")

    def type_text(self, text):
        """模拟键盘输入文本"""
        try:
            pyautogui.typewrite(text)
            self.logger.info(f"Typed text: {text}")
        except Exception as e:
            self.logger.error(f"Error typing text: {e}")

    # def find_and_click_image(self, image_path, confidence=0.9, click_position='center', click_time='single'):
    #     """
    #     识别图片并点击指定位置
    #     :param image_path: 图片路径
    #     :param confidence: 匹配置信度
    #     :param click_position: 点击位置 ('center' 或 'top-left')
    #     """
    #     try:
    #         location = pyautogui.locateOnScreen(image_path, confidence=confidence)
    #         if location:
    #             self.logger.info(f"test")
    #             # 根据参数计算点击坐标
    #             if click_position == 'top-left':
    #                 x, y = location.left, location.top
    #             elif click_position == 'top-right':
    #                 x, y = location.left+location.width, location.top+location.height/2
    #             else:
    #                 x, y = pyautogui.center(location)
    #
    #             if click_time == 'single':
    #                 self.mouse_singleclick(x, y)
    #             elif click_time == 'right':
    #                 self.mouse_rightclick(x, y)
    #             elif click_time == 'double':
    #                 self.mouse_doubleclick(x, y)
    #
    #             self.logger.info(f"Clicked {click_position} at ({x}, {y})")
    #             return True
    #         else:
    #             self.logger.warning(f"Image {image_path} not found")
    #             return False
    #     except Exception as e:
    #         self.logger.error(f"Image recognition failed: {e}")
    #         return False
    def find_and_click_image(self, image_path, confidence=0.9, click_position='center', click_time='single'):
        """
        识别图片并点击指定位置，若未找到图片则等待用户确认
        :param image_path: 图片路径
        :param confidence: 匹配置信度
        :param click_position: 点击位置 ('center'/'top-left'/'top-right')
        :param click_time: 点击类型 ('single'/'right'/'double')
        :return: 成功点击返回True，用户中止返回False
        """
        while True:  # 添加重试循环
            try:
                location = pyautogui.locateOnScreen(image_path, confidence=confidence)
                if location:
                    # 计算点击坐标（新增top-right处理）
                    if click_position == 'top-left':
                        x, y = location.left, location.top
                    elif click_position == 'top-right':
                        x, y = location.left+location.width, location.top+location.height/2
                    else:  # center
                        x, y = pyautogui.center(location)

                    # 执行点击操作
                    if click_time == 'single':
                        self.mouse_singleclick(x, y)
                    elif click_time == 'right':
                        self.mouse_rightclick(x, y)
                    elif click_time == 'double':
                        self.mouse_doubleclick(x, y)

                    self.logger.info(f"Clicked {click_position} at ({x}, {y})")
                    return True

                else:
                    self.logger.warning(f"Image {image_path} not found")
                    # 未找到图片时请求用户确认
                    # if not self.get_yn_confirmation():
                    #     self.logger.info("Operation aborted by user")
                    #     return False  # 用户选择中止
                    # 用户选择继续，循环重试
                    self.logger.info("Retrying image recognition...")
                    time.sleep(5)  # 重试前短暂等待

            except Exception as e:
                self.logger.error(f"Image recognition failed: {e}")
                # 异常时同样请求用户确认
                # if not self.get_yn_confirmation():
                #     return False
                time.sleep(1)

    def add_task(self, task_func, *args):
        """添加任务到任务队列"""
        self.task_queue.put((task_func, args))
        self.logger.info(f"Added task: {task_func.__name__}")

    def worker(self):
        """工作线程，从任务队列中获取任务并执行"""
        while self.running:
            try:
                # 从队列中获取任务
                task_func, args = self.task_queue.get(timeout=1)
                # 执行任务
                task_func(*args)
                # 任务完成
                self.task_queue.task_done()
                self.logger.info(f"Task completed: {task_func.__name__}")
            except Exception as e:
                self.logger.warning(f"Task queue is empty or an error occurred: {e}")
                # 如果队列为空，继续等待
                continue

    def start_threads(self, num_threads):
        """启动指定数量的工作线程"""
        for _ in range(num_threads):
            thread = threading.Thread(target=self.worker)
            thread.daemon = True  # 设置为守护线程
            thread.start()
            self.threads.append(thread)
        self.logger.info(f"Started {num_threads} worker threads")

    def start_alert_thread(self):
        """启动弹框处理线程"""
        self.alert_thread = threading.Thread(target=self.handle_alert)
        self.alert_thread.daemon = True  # 设置为守护线程
        self.alert_thread.start()
        self.logger.info("Started alert handling thread")

    def handle_alert(self):
        """处理异常弹框的接口函数"""
        while self.running:
            try:
                # 检查是否存在弹框（这里假设弹框上有确定按钮）
                if self.find_and_click_image("alert_button.png"):
                    self.logger.info("Alert handled")
            except Exception as e:
                self.logger.error(f"Error handling alert: {e}")
            # 每隔1秒检查一次
            time.sleep(1)

    def find_click_and_type(self, image_path, input_text='1',
                            click_position1='top-right', click_time1='double',
                            find_success_msg=None, type_success_msg=None,
                            searching_msg=None, wait_after_click=2):
        """
        查找图片并执行点击+输入操作的封装接口

        参数:
        automation -- 自动化操作对象(需包含find_and_click_image, type_text, logger方法)
        image_path -- 要查找的图片路径
        input_text -- 要输入的文本内容
        click_position -- 点击位置 (默认'top-right')
        click_time -- 点击类型 (默认'double')
        find_success_msg -- 找到图片时的日志消息 (默认自动生成)
        type_success_msg -- 输入成功时的日志消息 (默认自动生成)
        searching_msg -- 查找过程中的日志消息 (默认自动生成)
        wait_after_click -- 点击后的等待时间(秒) (默认2)
        """
        # 设置默认日志消息
        if not find_success_msg:
            find_success_msg = f"双击{image_path}"
        if not type_success_msg:
            type_success_msg = f"成功输入msbc {image_path}参数"
        if not searching_msg:
            searching_msg = f"继续寻找{image_path}"

        while True:
            if self.find_and_click_image(image_path, click_position=click_position1, click_time=click_time1):
                self.logger.info(find_success_msg)
                time.sleep(wait_after_click)
                pyautogui.typewrite(input_text)
                self.logger.info(type_success_msg)
                return True  # 操作成功完成
            else:
                self.logger.info(searching_msg)

    def get_yn_confirmation(self, prompt="Continue?"):
        while True:
            response = input(f"\n{prompt} [Y/N]: ").strip().lower()
            if response in ('y', 'yes'):
                return True
            if response in ('n', 'no'):
                return False
            print("无效输入! 请输入 Y 或 N")
            self.logger.info("无效输入! 请输入 Y 或 N")

    def stop(self):
        """停止所有线程"""
        self.running = False
        # 等待所有线程完成
        for thread in self.threads:
            thread.join()
        if self.alert_thread:
            self.alert_thread.join()
        self.logger.info("All threads stopped")

    def run(self):
        """运行框架"""
        self.logger.info("Starting UI automation framework")
        try:
            # 启动弹框处理线程
            #self.start_alert_thread()
            # 启动工作线程
            self.start_threads(1)  # 这里启动3个工作线程，可以根据需要调整
            # 等待所有任务完成
            self.task_queue.join()
            # 停止所有线程
            self.stop()
            self.logger.info("UI automation framework finished successfully")
        except Exception as e:
            self.logger.error(f"Error running UI automation framework: {e}")