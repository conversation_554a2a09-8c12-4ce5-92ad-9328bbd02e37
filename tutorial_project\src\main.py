"""
UI自动化测试框架主程序
演示完整的框架使用方法
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import AutomationSettings, initialize_settings
from src.core.automation_framework import UIAutomationFramework
from src.core.logger import get_logger
from src.tests.bluetooth_tests import BluetoothTestSuite
from src.tests.audio_tests import AudioTestSuite


class AutomationTestRunner:
    """自动化测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        # 初始化配置
        initialize_settings()
        
        # 创建日志记录器
        self.logger = get_logger("TestRunner")
        
        # 创建自动化框架
        self.automation = UIAutomationFramework(
            default_confidence=AutomationSettings.IMAGE_RECOGNITION["DEFAULT_CONFIDENCE"],
            default_timeout=AutomationSettings.IMAGE_RECOGNITION["DEFAULT_TIMEOUT"],
            max_workers=AutomationSettings.TASK_MANAGEMENT["MAX_WORKERS"]
        )
        
        # 创建测试套件
        self.bluetooth_tests = BluetoothTestSuite(self.automation)
        self.audio_tests = AudioTestSuite(self.automation)
        
        self.logger.info("自动化测试运行器初始化完成")
    
    def run_demo_tasks(self):
        """运行演示任务"""
        self.logger.log_test_start("演示任务执行")
        
        try:
            # 添加一些演示任务
            self.automation.add_task(
                "demo_task_1",
                "演示任务1 - 鼠标移动",
                self._demo_mouse_movement
            )
            
            self.automation.add_task(
                "demo_task_2", 
                "演示任务2 - 键盘输入",
                self._demo_keyboard_input
            )
            
            self.automation.add_task(
                "demo_task_3",
                "演示任务3 - 屏幕截图",
                self._demo_screenshot,
                priority=1  # 高优先级
            )
            
            # 启动任务执行
            self.automation.start_tasks()
            
            # 等待所有任务完成
            self.automation.wait_for_all_tasks(timeout=60)
            
            # 打印任务统计
            self.automation.print_task_statistics()
            
            self.logger.log_test_end("演示任务执行", success=True)
            
        except Exception as e:
            self.logger.error(f"演示任务执行失败: {e}")
            self.logger.log_test_end("演示任务执行", success=False)
        
        finally:
            # 停止任务管理器
            self.automation.stop_tasks()
    
    def _demo_mouse_movement(self):
        """演示鼠标移动"""
        self.logger.log_action("开始鼠标移动演示")
        
        # 获取当前鼠标位置
        current_pos = self.automation.get_mouse_position()
        self.logger.info(f"当前鼠标位置: {current_pos}")
        
        # 移动鼠标到几个不同位置
        positions = [(100, 100), (200, 200), (300, 150), current_pos]
        
        for pos in positions:
            self.automation.move_mouse(pos[0], pos[1], duration=1.0)
            self.logger.log_action(f"鼠标移动到: {pos}")
            time.sleep(1)
        
        self.logger.log_result("鼠标移动演示完成")
    
    def _demo_keyboard_input(self):
        """演示键盘输入"""
        self.logger.log_action("开始键盘输入演示")
        
        # 模拟打开记事本（Windows）
        self.automation.press_key("win")
        time.sleep(1)
        self.automation.type_text("notepad")
        time.sleep(1)
        self.automation.press_key("enter")
        time.sleep(3)
        
        # 输入一些文本
        demo_text = "这是UI自动化框架的演示文本\n"
        demo_text += "框架功能包括:\n"
        demo_text += "1. 图像识别和匹配\n"
        demo_text += "2. 鼠标和键盘操作\n"
        demo_text += "3. 任务队列管理\n"
        demo_text += "4. 异常处理机制\n"
        demo_text += "5. 日志记录系统\n"
        
        self.automation.type_text(demo_text)
        
        self.logger.log_result("键盘输入演示完成")
    
    def _demo_screenshot(self):
        """演示屏幕截图"""
        self.logger.log_action("开始屏幕截图演示")
        
        # 使用图像匹配器进行截图
        screenshot = self.automation.image_matcher.capture_screen()
        
        if screenshot:
            # 保存截图
            screenshot_path = AutomationSettings.get_report_path("demo_screenshot.png")
            screenshot.save(screenshot_path)
            self.logger.log_result(f"截图已保存: {screenshot_path}")
        else:
            self.logger.error("截图失败")
    
    def run_bluetooth_tests(self):
        """运行蓝牙测试"""
        self.logger.log_test_start("蓝牙功能测试")
        
        try:
            # 运行完整的蓝牙测试套件
            results = self.bluetooth_tests.run_full_test_suite()
            
            # 输出测试结果
            self.logger.info("=== 蓝牙测试结果 ===")
            for test_name, result in results.items():
                status = "通过" if result else "失败"
                self.logger.info(f"{test_name}: {status}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"蓝牙测试执行失败: {e}")
            return {}
    
    def run_audio_tests(self):
        """运行音频测试"""
        self.logger.log_test_start("音频功能测试")
        
        try:
            # 运行综合音频测试
            results = self.audio_tests.run_comprehensive_audio_test()
            
            # 输出测试结果
            self.logger.info("=== 音频测试结果 ===")
            for test_name, result in results.items():
                if test_name == "overall_score":
                    self.logger.info(f"总体得分: {result:.1f}%")
                else:
                    status = "通过" if result else "失败"
                    self.logger.info(f"{test_name}: {status}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"音频测试执行失败: {e}")
            return {}
    
    def run_full_test_suite(self):
        """运行完整测试套件"""
        self.logger.log_test_start("完整测试套件")
        
        all_results = {}
        
        try:
            # 1. 运行演示任务
            self.logger.info("=== 第一阶段：演示任务 ===")
            self.run_demo_tasks()
            
            # 2. 运行蓝牙测试
            self.logger.info("=== 第二阶段：蓝牙测试 ===")
            bluetooth_results = self.run_bluetooth_tests()
            all_results["bluetooth"] = bluetooth_results
            
            # 3. 运行音频测试
            self.logger.info("=== 第三阶段：音频测试 ===")
            audio_results = self.run_audio_tests()
            all_results["audio"] = audio_results
            
            # 4. 生成总体报告
            self._generate_summary_report(all_results)
            
            self.logger.log_test_end("完整测试套件", success=True)
            
        except Exception as e:
            self.logger.error(f"完整测试套件执行失败: {e}")
            self.logger.log_test_end("完整测试套件", success=False)
        
        return all_results
    
    def _generate_summary_report(self, results):
        """生成总结报告"""
        self.logger.info("=== 测试总结报告 ===")
        
        total_tests = 0
        passed_tests = 0
        
        for category, category_results in results.items():
            if isinstance(category_results, dict):
                category_total = len([k for k in category_results.keys() 
                                    if k != "overall_score"])
                category_passed = sum([1 for k, v in category_results.items() 
                                     if k != "overall_score" and v])
                
                total_tests += category_total
                passed_tests += category_passed
                
                self.logger.info(f"{category}测试: {category_passed}/{category_total} 通过")
        
        if total_tests > 0:
            success_rate = (passed_tests / total_tests) * 100
            self.logger.info(f"总体通过率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        # 获取异常统计
        exception_stats = self.automation.exception_handler.get_exception_statistics()
        if exception_stats:
            self.logger.info("异常统计:")
            for exception_type, count in exception_stats.items():
                self.logger.info(f"  {exception_type}: {count} 次")


def main():
    """主函数"""
    print("=" * 60)
    print("UI自动化测试框架演示程序")
    print("=" * 60)
    
    try:
        # 创建测试运行器
        runner = AutomationTestRunner()
        
        # 显示菜单
        while True:
            print("\n请选择要执行的操作:")
            print("1. 运行演示任务")
            print("2. 运行蓝牙测试")
            print("3. 运行音频测试")
            print("4. 运行完整测试套件")
            print("5. 查看当前配置")
            print("6. 退出程序")
            
            choice = input("\n请输入选择 (1-6): ").strip()
            
            if choice == "1":
                runner.run_demo_tasks()
            elif choice == "2":
                runner.run_bluetooth_tests()
            elif choice == "3":
                runner.run_audio_tests()
            elif choice == "4":
                runner.run_full_test_suite()
            elif choice == "5":
                AutomationSettings.print_current_settings()
            elif choice == "6":
                print("程序退出")
                break
            else:
                print("无效选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        print("感谢使用UI自动化测试框架！")


if __name__ == "__main__":
    main()
