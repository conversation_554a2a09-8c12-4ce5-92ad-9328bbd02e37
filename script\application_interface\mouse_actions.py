import pyautogui

from script.Framework.framework import UIAutomationFramework
import time
from script.application_interface.image_resources import image_resources
import os
# 导入检测器类
from script.Framework.framework import log_dir
from script.application_interface.visual_stability import VisualStabilityDetector
from script.application_interface.visual_stability import example_usage

def test_task1(automation):
    # automation.find_and_click_image(image_resources.ljw, click_time='single')
    # if not automation.get_yn_confirmation():
    #     print("操作已中止")
    #     exit()

    # print("执行关键操作...")
    # time.sleep(2)
    # automation.find_click_and_type(image_resources.ljw, input_text='13.3', click_position='center')
    # example_usage()

    # /**************************以上调试*******************************/

    """示例测试任务1，使用鼠标点击"""
    automation.logger.info("Starting test task 1")

    time.sleep(2)
    automation.logger.info("F5进入蓝牙选项")
    pyautogui.hotkey('F5')

    time.sleep(2)
    automation.logger.info("双击进入bluetooth_audio")
    automation.find_and_click_image(image_resources.bluetooth_audio, click_time='double')

    # # /**************************弹窗，耳机进入配对状态*******************************/

    time.sleep(2)
    automation.logger.info("单击选择蓝牙设备")
    automation.find_and_click_image(image_resources.selected_device, click_time='single')

    time.sleep(2)
    automation.logger.info("双击蓝牙设备")
    automation.find_and_click_image(image_resources.device, click_time='double')


    automation.logger.info("退出关闭连接弹框")
    pyautogui.hotkey('Esc')
    time.sleep(2)

    #/**************************连接蓝牙*******************************/

    time.sleep(2)
    automation.logger.info("单击Activated_Profile")
    automation.find_and_click_image(image_resources.Activated_Profile, click_time='single')

    time.sleep(2)
    automation.logger.info("双击HFP_AG")
    automation.find_and_click_image(image_resources.HFP_AG, click_time='double')

    time.sleep(2)
    automation.logger.info("单击Profile_Settings")
    automation.find_and_click_image(image_resources.Profile_Settings, click_time='single')

    time.sleep(2)
    automation.logger.info("单击Select_msbccvsd_path")
    automation.find_and_click_image(image_resources.Select_msbccvsd, click_time='single')

    time.sleep(2)
    automation.logger.info("单击msbc_path")
    automation.find_and_click_image(image_resources.msbc_blue, click_time='single')

    time.sleep(2)
    automation.logger.info("单击ok")
    automation.find_and_click_image(image_resources.HFP_AG_Settings_ok, click_time='single')
    time.sleep(2)
    automation.logger.info("单击Connect")
    automation.find_and_click_image(image_resources.Connect, click_time='single')


    # /**************************弹窗，佩戴耳机*******************************/

    # if not automation.get_yn_confirmation():
    #     print("操作已中止")
    #     exit()

    # /**************************接通电话*******************************/

    time.sleep(10)
    automation.logger.info("单击Ring")
    automation.find_and_click_image(image_resources.Ring, click_time='single')

    time.sleep(4)

    automation.logger.info("选择音量8/15")
    automation.find_and_click_image(image_resources.volume7, click_position='top-right', click_time='single')
    time.sleep(4)
    pyautogui.hotkey('ctrl', 'a')
    time.sleep(4)
    pyautogui.hotkey('Backspace')

    automation.type_text("15")

    time.sleep(5)
    automation.logger.info("单击Accept_Call")
    automation.find_and_click_image(image_resources.Accept_Call, click_time='single')


    #/**************************执行用例*******************************/
    time.sleep(5)
    automation.logger.info("跳转用例界面")
    pyautogui.hotkey('Esc')
    time.sleep(2)
    pyautogui.hotkey('Esc')


    time.sleep(5)
    automation.logger.info("右键勾选用例")
    automation.find_and_click_image(image_resources.Define_Setup, click_time='right')

    automation.logger.info("Completed test task 1")

    time.sleep(2)
    automation.logger.info("勾选SMD_collections_path")
    automation.find_and_click_image(image_resources.SMD_collections, click_time='single')
    automation.logger.info("Completed test task 1")

    time.sleep(2)
    automation.logger.info("勾选Marks_from_path")
    automation.find_and_click_image(image_resources.Marks_from, click_time='single')
    automation.logger.info("Completed test task 1")

    time.sleep(2)
    automation.logger.info("勾选WB_MAX")
    automation.find_and_click_image(image_resources.WB_MAX, click_time='single')
    automation.logger.info("Completed test task 1")

    time.sleep(5)
    automation.logger.info("快捷方式运行用例")
    pyautogui.hotkey('ctrl', 'enter')


    time.sleep(2)
    automation.logger.info("单击Define_ok")
    automation.find_and_click_image(image_resources.Define_Setup_ok, click_time='single')

    #/**************************输入宽窄带参数*******************************/

    automation.find_click_and_type(image_resources.D_RCV_NET, input_text='16.3',click_position1='top-right', click_time1='double')

    time.sleep(2)
    automation.logger.info("单击Edit_varrible_ok")
    pyautogui.hotkey('enter')

    time.sleep(2)
    automation.find_click_and_type(image_resources.D_SR_REA, input_text='1.1', click_position1='top-right', click_time1='double')

    time.sleep(5)
    automation.logger.info("单击Edit_varrible_ok")
    pyautogui.hotkey('enter')

    time.sleep(2)
    automation.logger.info("双击Edit_correction_ok")
    automation.find_and_click_image(image_resources.Edit_correction_ok, click_time='double')

    time.sleep(2)
    automation.find_click_and_type(image_resources.D_SND_NET, input_text='9', click_position1='top-right', click_time1='double')

    time.sleep(2)
    automation.logger.info("单击Edit_varrible_ok")
    pyautogui.hotkey('enter')

    time.sleep(2)
    automation.find_click_and_type(image_resources.D_SR_REA, input_text='1.1', click_position1='top-right', click_time1='double')

    time.sleep(5)
    automation.logger.info("单击Edit_varrible_ok")
    pyautogui.hotkey('enter')

    time.sleep(2)
    automation.logger.info("单击Edit_correction_ok")
    automation.find_and_click_image(image_resources.Edit_correction_ok, click_time='double')
    time.sleep(2)
    automation.find_click_and_type(image_resources.D_SR_REA, input_text='1.1', click_position1='top-right', click_time1='double')

    time.sleep(5)
    automation.logger.info("单击Edit_varrible_ok")
    pyautogui.hotkey('enter')

    time.sleep(2)
    automation.logger.info("单击Edit_correction_ok")
    automation.find_and_click_image(image_resources.Edit_correction_ok, click_time='double')

    automation.logger.info("等待用例执行完成")
    #/**************************统计用例测试时长*******************************/
    # time.sleep(600)
    # time.sleep(600)
    # time.sleep(600)
    # time.sleep(600)

    #如果跑到最后一条用例（叫什么名字）
    # {
    #       automation.find_click_and_type(image_resources.D_SR_REA, input_text='1.1', click_position1='top-right', click_time1='double')
    #      find_and_click_image(image_resources.Edit_correction_ok, click_time='double')
    #     example_usage()
    # }

    #/**************************F5跳转，修改8音量*******************************/
    time.sleep(2)
    automation.logger.info("F5进入蓝牙选项")
    pyautogui.hotkey('F5')

    time.sleep(2)
    automation.logger.info("双击进入bluetooth_audio")
    automation.find_and_click_image(image_resources.bluetooth_audio, click_time='double')


    time.sleep(4)
    automation.logger.info("选择音量8/15")
    automation.find_and_click_image(image_resources.volume7, click_position='top-right', click_time='single')
    time.sleep(4)
    pyautogui.hotkey('ctrl', 'a')
    time.sleep(4)
    pyautogui.hotkey('Backspace')
    automation.type_text("15")

    automation.logger.info("跳转用例界面")
    pyautogui.hotkey('Esc')
    time.sleep(2)
    pyautogui.hotkey('Esc')

    #/**************************最大音量用例执行*******************************/


    #/**************************测试3QUEST*******************************/
    time.sleep(2)
    automation.logger.info("F5进入蓝牙选项")
    pyautogui.hotkey('F5')

    time.sleep(2)
    automation.logger.info("查找bluetooth_right")
    automation.find_and_click_image(image_resources.bluetooth_right, click_time='single')

    time.sleep(3)
    automation.logger.info("choose_3QUEST")
    automation.find_and_click_image(image_resources.choose_3QUEST, click_time='double')

    time.sleep(5)
    automation.logger.info("双击进入bluetooth_audio")
    automation.find_and_click_image(image_resources.bluetooth_audio, click_time='double')

    automation.logger.info("跳转用例界面")
    pyautogui.hotkey('Esc')
    time.sleep(2)
    pyautogui.hotkey('Esc')

    time.sleep(5)
    automation.logger.info("右键勾选用例")
    automation.find_and_click_image(image_resources.finish_HEWB, click_time='right')
    automation.logger.info("lastHEWB")

    time.sleep(2)
    automation.logger.info("勾选WB_3QUEST_path")
    automation.find_and_click_image(image_resources.WB_3QUEST, click_time='single')
    automation.logger.info("Completed test task 1")

