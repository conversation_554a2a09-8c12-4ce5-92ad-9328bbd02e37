[32m[2025-04-22 20:20:47][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 20:20:52][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 20:20:52][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 20:20:52][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 20:20:52][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 20:20:52][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 20:20:57][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 20:20:57][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 20:20:57][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 20:20:58][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 20:20:58][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 20:20:58][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 20:26:27][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 20:26:32][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 20:26:32][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 20:26:32][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 20:26:32][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 20:26:32][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 20:26:37][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 20:26:37][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 20:26:37][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 20:26:39][INFO][UIAutomation] selected_device[0m
[32m[2025-04-22 20:26:39][INFO][UIAutomation] Clicked at position (958, 252)[0m
[32m[2025-04-22 20:26:39][INFO][UIAutomation] Clicked center at (958, 252)[0m
[32m[2025-04-22 20:26:39][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 20:26:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 20:26:40][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 20:26:40][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 20:41:02][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 20:41:07][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 20:41:07][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 20:41:07][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 20:41:07][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 20:41:07][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 20:41:12][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 20:41:13][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 20:41:13][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 20:41:15][INFO][UIAutomation] 选择蓝牙设备[0m
[32m[2025-04-22 20:41:15][INFO][UIAutomation] Clicked at position (958, 252)[0m
[32m[2025-04-22 20:41:15][INFO][UIAutomation] Clicked center at (958, 252)[0m
[32m[2025-04-22 20:41:15][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-22 20:41:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 20:41:15][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 20:41:16][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 20:41:16][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 20:41:16][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 20:42:27][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 20:42:32][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 20:42:32][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 20:42:32][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 20:42:32][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 20:42:32][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 20:42:34][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 20:42:34][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 20:42:34][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 20:42:36][INFO][UIAutomation] 选择蓝牙设备[0m
[32m[2025-04-22 20:42:37][INFO][UIAutomation] Clicked at position (958, 252)[0m
[32m[2025-04-22 20:42:37][INFO][UIAutomation] Clicked center at (958, 252)[0m
[32m[2025-04-22 20:42:39][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 20:42:39][INFO][UIAutomation] Clicked at position (999, 334)[0m
[32m[2025-04-22 20:42:39][INFO][UIAutomation] Clicked center at (999, 334)[0m
[32m[2025-04-22 20:42:39][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 20:42:40][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 20:42:40][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 20:42:40][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 20:47:12][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 20:47:17][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 20:47:17][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 20:47:17][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 20:47:17][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 20:47:17][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 20:47:19][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 20:47:19][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 20:47:19][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 20:47:21][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 20:47:21][INFO][UIAutomation] Clicked at position (958, 252)[0m
[32m[2025-04-22 20:47:21][INFO][UIAutomation] Clicked center at (958, 252)[0m
[32m[2025-04-22 20:47:23][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 20:47:23][INFO][UIAutomation] Clicked at position (999, 334)[0m
[32m[2025-04-22 20:47:23][INFO][UIAutomation] Clicked center at (999, 334)[0m
[32m[2025-04-22 20:47:25][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 20:47:25][INFO][UIAutomation] Clicked at position (957, 274)[0m
[32m[2025-04-22 20:47:25][INFO][UIAutomation] Clicked center at (957, 274)[0m
[32m[2025-04-22 20:47:27][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 20:47:28][INFO][UIAutomation] Clicked at position (1189, 435)[0m
[32m[2025-04-22 20:47:28][INFO][UIAutomation] Clicked center at (1189, 435)[0m
[32m[2025-04-22 20:47:28][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 20:47:29][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 20:47:29][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 20:47:29][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 20:52:56][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 20:53:01][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 20:53:01][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 20:53:01][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 20:53:01][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 20:53:01][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 20:53:03][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 20:53:03][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 20:53:03][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 20:53:05][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 20:53:05][INFO][UIAutomation] Clicked at position (958, 252)[0m
[32m[2025-04-22 20:53:05][INFO][UIAutomation] Clicked center at (958, 252)[0m
[32m[2025-04-22 20:53:07][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 20:53:07][INFO][UIAutomation] Clicked at position (999, 334)[0m
[32m[2025-04-22 20:53:07][INFO][UIAutomation] Clicked center at (999, 334)[0m
[32m[2025-04-22 20:53:09][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 20:53:09][INFO][UIAutomation] Clicked at position (957, 274)[0m
[32m[2025-04-22 20:53:09][INFO][UIAutomation] Clicked center at (957, 274)[0m
[32m[2025-04-22 20:53:11][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 20:53:12][INFO][UIAutomation] Clicked at position (1189, 435)[0m
[32m[2025-04-22 20:53:12][INFO][UIAutomation] Clicked center at (1189, 435)[0m
[32m[2025-04-22 20:53:14][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 20:53:14][INFO][UIAutomation] Clicked at position (1491, 276)[0m
[32m[2025-04-22 20:53:14][INFO][UIAutomation] Clicked center at (1491, 276)[0m
[32m[2025-04-22 20:53:16][INFO][UIAutomation] 单击Selecct_msbc[0m
[32m[2025-04-22 20:53:16][INFO][UIAutomation] Clicked at position (1649, 638)[0m
[32m[2025-04-22 20:53:16][INFO][UIAutomation] Clicked center at (1649, 638)[0m
[32m[2025-04-22 20:53:16][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 20:53:17][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 20:53:17][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 20:53:17][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 20:58:28][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 20:58:33][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 20:58:33][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 20:58:33][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 20:58:33][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 20:58:33][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 20:58:35][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 20:58:35][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 20:58:35][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 20:58:37][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 20:58:37][INFO][UIAutomation] Clicked at position (958, 252)[0m
[32m[2025-04-22 20:58:37][INFO][UIAutomation] Clicked center at (958, 252)[0m
[32m[2025-04-22 20:58:39][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 20:58:39][INFO][UIAutomation] Clicked at position (999, 334)[0m
[32m[2025-04-22 20:58:39][INFO][UIAutomation] Clicked center at (999, 334)[0m
[32m[2025-04-22 20:58:41][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 20:58:41][INFO][UIAutomation] Clicked at position (957, 274)[0m
[32m[2025-04-22 20:58:41][INFO][UIAutomation] Clicked center at (957, 274)[0m
[32m[2025-04-22 20:58:43][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 20:58:44][INFO][UIAutomation] Clicked at position (1189, 435)[0m
[32m[2025-04-22 20:58:44][INFO][UIAutomation] Clicked center at (1189, 435)[0m
[32m[2025-04-22 20:58:46][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 20:58:46][INFO][UIAutomation] Clicked at position (1491, 276)[0m
[32m[2025-04-22 20:58:46][INFO][UIAutomation] Clicked center at (1491, 276)[0m
[32m[2025-04-22 20:58:48][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 20:58:48][INFO][UIAutomation] Clicked at position (1668, 638)[0m
[32m[2025-04-22 20:58:48][INFO][UIAutomation] Clicked center at (1668, 638)[0m
[32m[2025-04-22 20:58:50][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 20:58:50][INFO][UIAutomation] Clicked at position (1651, 670)[0m
[32m[2025-04-22 20:58:50][INFO][UIAutomation] Clicked center at (1651, 670)[0m
[32m[2025-04-22 20:58:50][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 20:58:51][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 20:58:51][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 20:58:51][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:00:57][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:01:02][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:01:02][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:01:02][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:01:02][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:01:02][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:01:04][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:01:04][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:01:04][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:01:06][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:01:07][INFO][UIAutomation] Clicked at position (958, 252)[0m
[32m[2025-04-22 21:01:07][INFO][UIAutomation] Clicked center at (958, 252)[0m
[32m[2025-04-22 21:01:09][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:01:09][INFO][UIAutomation] Clicked at position (999, 334)[0m
[32m[2025-04-22 21:01:09][INFO][UIAutomation] Clicked center at (999, 334)[0m
[32m[2025-04-22 21:01:11][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:01:11][INFO][UIAutomation] Clicked at position (957, 274)[0m
[32m[2025-04-22 21:01:11][INFO][UIAutomation] Clicked center at (957, 274)[0m
[32m[2025-04-22 21:01:13][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:01:13][INFO][UIAutomation] Clicked at position (1189, 435)[0m
[32m[2025-04-22 21:01:13][INFO][UIAutomation] Clicked center at (1189, 435)[0m
[32m[2025-04-22 21:01:15][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:01:15][INFO][UIAutomation] Clicked at position (1491, 276)[0m
[32m[2025-04-22 21:01:15][INFO][UIAutomation] Clicked center at (1491, 276)[0m
[32m[2025-04-22 21:01:17][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:01:18][INFO][UIAutomation] Clicked at position (1668, 638)[0m
[32m[2025-04-22 21:01:18][INFO][UIAutomation] Clicked center at (1668, 638)[0m
[32m[2025-04-22 21:01:20][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:01:20][INFO][UIAutomation] Clicked at position (1651, 670)[0m
[32m[2025-04-22 21:01:20][INFO][UIAutomation] Clicked center at (1651, 670)[0m
[32m[2025-04-22 21:01:22][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:01:22][INFO][UIAutomation] Clicked at position (1622, 804)[0m
[32m[2025-04-22 21:01:22][INFO][UIAutomation] Clicked center at (1622, 804)[0m
[32m[2025-04-22 21:01:22][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:01:23][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:01:23][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:01:23][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:05:36][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:05:41][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:05:41][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:05:41][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:05:41][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:05:41][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:05:43][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:05:44][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:05:44][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:05:46][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:05:46][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:05:46][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:05:48][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:05:48][INFO][UIAutomation] Clicked at position (1077, 355)[0m
[32m[2025-04-22 21:05:48][INFO][UIAutomation] Clicked center at (1077, 355)[0m
[32m[2025-04-22 21:05:50][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:05:50][INFO][UIAutomation] Clicked at position (1035, 295)[0m
[32m[2025-04-22 21:05:50][INFO][UIAutomation] Clicked center at (1035, 295)[0m
[32m[2025-04-22 21:05:52][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:05:52][INFO][UIAutomation] Clicked at position (1267, 456)[0m
[32m[2025-04-22 21:05:52][INFO][UIAutomation] Clicked center at (1267, 456)[0m
[32m[2025-04-22 21:05:54][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:05:55][INFO][UIAutomation] Clicked at position (1569, 297)[0m
[32m[2025-04-22 21:05:55][INFO][UIAutomation] Clicked center at (1569, 297)[0m
[32m[2025-04-22 21:05:57][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:05:57][INFO][UIAutomation] Clicked at position (1746, 659)[0m
[32m[2025-04-22 21:05:57][INFO][UIAutomation] Clicked center at (1746, 659)[0m
[32m[2025-04-22 21:05:59][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:05:59][INFO][UIAutomation] Clicked at position (1729, 691)[0m
[32m[2025-04-22 21:05:59][INFO][UIAutomation] Clicked center at (1729, 691)[0m
[32m[2025-04-22 21:06:01][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:06:01][INFO][UIAutomation] Clicked at position (1700, 825)[0m
[32m[2025-04-22 21:06:01][INFO][UIAutomation] Clicked center at (1700, 825)[0m
[32m[2025-04-22 21:06:03][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:06:04][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:06:04][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:06:04][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 21:06:04][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:06:04][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:14:10][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:14:15][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:14:15][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:14:15][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:14:15][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:14:15][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:14:17][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:14:18][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:14:18][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:14:20][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:14:20][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:14:20][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:14:22][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-22 21:14:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:14:24][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-22 21:14:24][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:14:26][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-22 21:14:26][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:14:28][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-22 21:14:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:14:30][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:14:31][INFO][UIAutomation] Clicked at position (1727, 394)[0m
[32m[2025-04-22 21:14:31][INFO][UIAutomation] Clicked center at (1727, 394)[0m
[32m[2025-04-22 21:14:33][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-22 21:14:33][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:14:35][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:14:35][INFO][UIAutomation] Clicked at position (1317, 613)[0m
[32m[2025-04-22 21:14:35][INFO][UIAutomation] Clicked center at (1317, 613)[0m
[32m[2025-04-22 21:14:37][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:14:37][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:14:37][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:14:37][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:14:38][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:14:38][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:14:38][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:16:42][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:16:47][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:16:47][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:16:47][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:16:47][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:16:47][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:16:49][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:16:49][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:16:49][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:16:51][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:16:52][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:16:52][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:16:54][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:16:54][INFO][UIAutomation] Clicked at position (1090, 354)[0m
[32m[2025-04-22 21:16:54][INFO][UIAutomation] Clicked center at (1090, 354)[0m
[32m[2025-04-22 21:16:56][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:16:56][INFO][UIAutomation] Clicked at position (1035, 295)[0m
[32m[2025-04-22 21:16:56][INFO][UIAutomation] Clicked center at (1035, 295)[0m
[32m[2025-04-22 21:16:58][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:16:58][INFO][UIAutomation] Clicked at position (1267, 456)[0m
[32m[2025-04-22 21:16:58][INFO][UIAutomation] Clicked center at (1267, 456)[0m
[32m[2025-04-22 21:17:00][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:17:00][INFO][UIAutomation] Clicked at position (1569, 297)[0m
[32m[2025-04-22 21:17:00][INFO][UIAutomation] Clicked center at (1569, 297)[0m
[32m[2025-04-22 21:17:02][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:17:03][INFO][UIAutomation] Clicked at position (1746, 659)[0m
[32m[2025-04-22 21:17:03][INFO][UIAutomation] Clicked center at (1746, 659)[0m
[32m[2025-04-22 21:17:05][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:17:05][INFO][UIAutomation] Clicked at position (1729, 691)[0m
[32m[2025-04-22 21:17:05][INFO][UIAutomation] Clicked center at (1729, 691)[0m
[32m[2025-04-22 21:17:07][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:17:07][INFO][UIAutomation] Clicked at position (1700, 825)[0m
[32m[2025-04-22 21:17:07][INFO][UIAutomation] Clicked center at (1700, 825)[0m
[32m[2025-04-22 21:17:09][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:17:09][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:17:09][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:17:09][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 21:17:09][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:17:09][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:18:26][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:18:31][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:18:31][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:18:31][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:18:31][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:18:31][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:18:33][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:18:33][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:18:33][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:18:35][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:18:35][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:18:35][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:18:37][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:18:37][INFO][UIAutomation] Clicked at position (1090, 354)[0m
[32m[2025-04-22 21:18:37][INFO][UIAutomation] Clicked center at (1090, 354)[0m
[32m[2025-04-22 21:18:39][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:18:40][INFO][UIAutomation] Clicked at position (1035, 295)[0m
[32m[2025-04-22 21:18:40][INFO][UIAutomation] Clicked center at (1035, 295)[0m
[32m[2025-04-22 21:18:42][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:18:42][INFO][UIAutomation] Clicked at position (1267, 456)[0m
[32m[2025-04-22 21:18:42][INFO][UIAutomation] Clicked center at (1267, 456)[0m
[32m[2025-04-22 21:18:44][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:18:44][INFO][UIAutomation] Clicked at position (1569, 297)[0m
[32m[2025-04-22 21:18:44][INFO][UIAutomation] Clicked center at (1569, 297)[0m
[32m[2025-04-22 21:18:46][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:18:46][INFO][UIAutomation] Clicked at position (1746, 659)[0m
[32m[2025-04-22 21:18:46][INFO][UIAutomation] Clicked center at (1746, 659)[0m
[32m[2025-04-22 21:18:48][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:18:48][INFO][UIAutomation] Clicked at position (1729, 691)[0m
[32m[2025-04-22 21:18:48][INFO][UIAutomation] Clicked center at (1729, 691)[0m
[32m[2025-04-22 21:18:50][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:18:51][INFO][UIAutomation] Clicked at position (1700, 825)[0m
[32m[2025-04-22 21:18:51][INFO][UIAutomation] Clicked center at (1700, 825)[0m
[32m[2025-04-22 21:18:53][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:18:53][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:18:53][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:18:55][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-22 21:18:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:18:57][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-22 21:18:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:18:57][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 21:18:57][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:18:57][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:22:58][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:23:03][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:23:03][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:23:03][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:23:03][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:23:03][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:23:05][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:23:05][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:23:05][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:23:07][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:23:07][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:23:07][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:23:09][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:23:09][INFO][UIAutomation] Clicked at position (1090, 354)[0m
[32m[2025-04-22 21:23:09][INFO][UIAutomation] Clicked center at (1090, 354)[0m
[32m[2025-04-22 21:23:11][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:23:12][INFO][UIAutomation] Clicked at position (1035, 295)[0m
[32m[2025-04-22 21:23:12][INFO][UIAutomation] Clicked center at (1035, 295)[0m
[32m[2025-04-22 21:23:14][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:23:14][INFO][UIAutomation] Clicked at position (1267, 456)[0m
[32m[2025-04-22 21:23:14][INFO][UIAutomation] Clicked center at (1267, 456)[0m
[32m[2025-04-22 21:23:16][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:23:16][INFO][UIAutomation] Clicked at position (1569, 297)[0m
[32m[2025-04-22 21:23:16][INFO][UIAutomation] Clicked center at (1569, 297)[0m
[32m[2025-04-22 21:23:18][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:23:18][INFO][UIAutomation] Clicked at position (1746, 659)[0m
[32m[2025-04-22 21:23:18][INFO][UIAutomation] Clicked center at (1746, 659)[0m
[32m[2025-04-22 21:23:20][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:23:20][INFO][UIAutomation] Clicked at position (1729, 691)[0m
[32m[2025-04-22 21:23:20][INFO][UIAutomation] Clicked center at (1729, 691)[0m
[32m[2025-04-22 21:23:22][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:23:23][INFO][UIAutomation] Clicked at position (1700, 825)[0m
[32m[2025-04-22 21:23:23][INFO][UIAutomation] Clicked center at (1700, 825)[0m
[32m[2025-04-22 21:23:25][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:23:25][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:23:25][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:23:30][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-22 21:23:30][INFO][UIAutomation] Clicked at position (1537, 317)[0m
[32m[2025-04-22 21:23:30][INFO][UIAutomation] Clicked center at (1537, 317)[0m
[32m[2025-04-22 21:23:35][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-22 21:23:35][INFO][UIAutomation] Clicked at position (1648, 317)[0m
[32m[2025-04-22 21:23:35][INFO][UIAutomation] Clicked center at (1648, 317)[0m
[32m[2025-04-22 21:23:35][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:23:36][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:23:36][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:23:36][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:31:47][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:31:52][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:31:52][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:31:52][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:31:52][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:31:52][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:31:54][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:31:54][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:31:54][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:31:56][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:31:57][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:31:57][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:31:59][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:31:59][INFO][UIAutomation] Clicked at position (1090, 354)[0m
[32m[2025-04-22 21:31:59][INFO][UIAutomation] Clicked center at (1090, 354)[0m
[32m[2025-04-22 21:32:01][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:32:01][INFO][UIAutomation] Clicked at position (1035, 295)[0m
[32m[2025-04-22 21:32:01][INFO][UIAutomation] Clicked center at (1035, 295)[0m
[32m[2025-04-22 21:32:03][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:32:03][INFO][UIAutomation] Clicked at position (1267, 456)[0m
[32m[2025-04-22 21:32:03][INFO][UIAutomation] Clicked center at (1267, 456)[0m
[32m[2025-04-22 21:32:05][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:32:06][INFO][UIAutomation] Clicked at position (1569, 297)[0m
[32m[2025-04-22 21:32:06][INFO][UIAutomation] Clicked center at (1569, 297)[0m
[32m[2025-04-22 21:32:08][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:32:08][INFO][UIAutomation] Clicked at position (1746, 659)[0m
[32m[2025-04-22 21:32:08][INFO][UIAutomation] Clicked center at (1746, 659)[0m
[32m[2025-04-22 21:32:10][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:32:10][INFO][UIAutomation] Clicked at position (1729, 691)[0m
[32m[2025-04-22 21:32:10][INFO][UIAutomation] Clicked center at (1729, 691)[0m
[32m[2025-04-22 21:32:12][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:32:12][INFO][UIAutomation] Clicked at position (1700, 825)[0m
[32m[2025-04-22 21:32:12][INFO][UIAutomation] Clicked center at (1700, 825)[0m
[32m[2025-04-22 21:32:14][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:32:14][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:32:14][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:32:19][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-22 21:32:20][INFO][UIAutomation] Clicked at position (1537, 317)[0m
[32m[2025-04-22 21:32:20][INFO][UIAutomation] Clicked center at (1537, 317)[0m
[32m[2025-04-22 21:32:25][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-22 21:32:25][INFO][UIAutomation] Clicked at position (1648, 317)[0m
[32m[2025-04-22 21:32:25][INFO][UIAutomation] Clicked center at (1648, 317)[0m
[32m[2025-04-22 21:32:25][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:32:26][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:32:26][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:32:26][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:33:59][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:34:04][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:34:04][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:34:04][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:34:04][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:34:04][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:34:06][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:34:07][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:34:07][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:34:09][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:34:09][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:34:09][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:34:11][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:34:11][INFO][UIAutomation] Clicked at position (1090, 354)[0m
[32m[2025-04-22 21:34:11][INFO][UIAutomation] Clicked center at (1090, 354)[0m
[32m[2025-04-22 21:34:13][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:34:13][INFO][UIAutomation] Clicked at position (1035, 295)[0m
[32m[2025-04-22 21:34:13][INFO][UIAutomation] Clicked center at (1035, 295)[0m
[32m[2025-04-22 21:34:15][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:34:16][INFO][UIAutomation] Clicked at position (1267, 456)[0m
[32m[2025-04-22 21:34:16][INFO][UIAutomation] Clicked center at (1267, 456)[0m
[32m[2025-04-22 21:34:18][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:34:18][INFO][UIAutomation] Clicked at position (1569, 297)[0m
[32m[2025-04-22 21:34:18][INFO][UIAutomation] Clicked center at (1569, 297)[0m
[32m[2025-04-22 21:34:20][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:34:20][INFO][UIAutomation] Clicked at position (1746, 659)[0m
[32m[2025-04-22 21:34:20][INFO][UIAutomation] Clicked center at (1746, 659)[0m
[32m[2025-04-22 21:34:22][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:34:22][INFO][UIAutomation] Clicked at position (1729, 691)[0m
[32m[2025-04-22 21:34:22][INFO][UIAutomation] Clicked center at (1729, 691)[0m
[32m[2025-04-22 21:34:24][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:34:24][INFO][UIAutomation] Clicked at position (1700, 825)[0m
[32m[2025-04-22 21:34:24][INFO][UIAutomation] Clicked center at (1700, 825)[0m
[32m[2025-04-22 21:34:26][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:34:27][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:34:27][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:34:32][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-22 21:34:32][INFO][UIAutomation] Clicked at position (1537, 317)[0m
[32m[2025-04-22 21:34:32][INFO][UIAutomation] Clicked center at (1537, 317)[0m
[32m[2025-04-22 21:34:37][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-22 21:34:37][INFO][UIAutomation] Clicked at position (1648, 317)[0m
[32m[2025-04-22 21:34:37][INFO][UIAutomation] Clicked center at (1648, 317)[0m
[32m[2025-04-22 21:34:39][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 21:34:39][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:34:39][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:36:36][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:36:41][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:36:41][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:36:41][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:36:41][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:36:41][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:36:43][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:36:43][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:36:43][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:36:45][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:36:45][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:36:45][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:36:47][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:36:47][INFO][UIAutomation] Clicked at position (1090, 354)[0m
[32m[2025-04-22 21:36:47][INFO][UIAutomation] Clicked center at (1090, 354)[0m
[32m[2025-04-22 21:36:49][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:36:49][INFO][UIAutomation] Clicked at position (1035, 295)[0m
[32m[2025-04-22 21:36:49][INFO][UIAutomation] Clicked center at (1035, 295)[0m
[32m[2025-04-22 21:36:51][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:36:52][INFO][UIAutomation] Clicked at position (1267, 456)[0m
[32m[2025-04-22 21:36:52][INFO][UIAutomation] Clicked center at (1267, 456)[0m
[32m[2025-04-22 21:36:54][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:36:54][INFO][UIAutomation] Clicked at position (1569, 297)[0m
[32m[2025-04-22 21:36:54][INFO][UIAutomation] Clicked center at (1569, 297)[0m
[32m[2025-04-22 21:36:56][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:36:56][INFO][UIAutomation] Clicked at position (1746, 659)[0m
[32m[2025-04-22 21:36:56][INFO][UIAutomation] Clicked center at (1746, 659)[0m
[32m[2025-04-22 21:36:58][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:36:58][INFO][UIAutomation] Clicked at position (1729, 691)[0m
[32m[2025-04-22 21:36:58][INFO][UIAutomation] Clicked center at (1729, 691)[0m
[32m[2025-04-22 21:37:00][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:37:00][INFO][UIAutomation] Clicked at position (1700, 825)[0m
[32m[2025-04-22 21:37:00][INFO][UIAutomation] Clicked center at (1700, 825)[0m
[32m[2025-04-22 21:37:02][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:37:03][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:37:03][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:37:08][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-22 21:37:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:37:13][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-22 21:37:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:37:13][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 21:37:15][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 21:37:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:37:15][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 21:37:17][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:37:18][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:37:18][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:37:18][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:39:28][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:39:33][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:39:33][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:39:33][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:39:33][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:39:33][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:39:35][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:39:35][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:39:35][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:39:37][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:39:37][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:39:37][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:39:39][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:39:39][INFO][UIAutomation] Clicked at position (1090, 354)[0m
[32m[2025-04-22 21:39:39][INFO][UIAutomation] Clicked center at (1090, 354)[0m
[32m[2025-04-22 21:39:41][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:39:42][INFO][UIAutomation] Clicked at position (1035, 295)[0m
[32m[2025-04-22 21:39:42][INFO][UIAutomation] Clicked center at (1035, 295)[0m
[32m[2025-04-22 21:39:44][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:39:44][INFO][UIAutomation] Clicked at position (1267, 456)[0m
[32m[2025-04-22 21:39:44][INFO][UIAutomation] Clicked center at (1267, 456)[0m
[32m[2025-04-22 21:39:46][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:39:46][INFO][UIAutomation] Clicked at position (1569, 297)[0m
[32m[2025-04-22 21:39:46][INFO][UIAutomation] Clicked center at (1569, 297)[0m
[32m[2025-04-22 21:39:48][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:39:48][INFO][UIAutomation] Clicked at position (1746, 659)[0m
[32m[2025-04-22 21:39:48][INFO][UIAutomation] Clicked center at (1746, 659)[0m
[32m[2025-04-22 21:39:50][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:39:50][INFO][UIAutomation] Clicked at position (1729, 691)[0m
[32m[2025-04-22 21:39:50][INFO][UIAutomation] Clicked center at (1729, 691)[0m
[32m[2025-04-22 21:39:52][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:39:53][INFO][UIAutomation] Clicked at position (1700, 825)[0m
[32m[2025-04-22 21:39:53][INFO][UIAutomation] Clicked center at (1700, 825)[0m
[32m[2025-04-22 21:39:55][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:39:55][INFO][UIAutomation] Clicked at position (1021, 315)[0m
[32m[2025-04-22 21:39:55][INFO][UIAutomation] Clicked center at (1021, 315)[0m
[32m[2025-04-22 21:40:00][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-22 21:40:00][INFO][UIAutomation] Clicked at position (1537, 317)[0m
[32m[2025-04-22 21:40:00][INFO][UIAutomation] Clicked center at (1537, 317)[0m
[32m[2025-04-22 21:40:05][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-22 21:40:05][INFO][UIAutomation] Clicked at position (1648, 317)[0m
[32m[2025-04-22 21:40:05][INFO][UIAutomation] Clicked center at (1648, 317)[0m
[32m[2025-04-22 21:40:05][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 21:40:08][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 21:40:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:40:08][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 21:40:10][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:40:11][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:40:11][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:40:11][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:44:18][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:44:23][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:44:23][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:44:23][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:44:23][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:44:23][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:44:25][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:44:25][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:44:25][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:44:27][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:44:28][INFO][UIAutomation] Clicked at position (1036, 273)[0m
[32m[2025-04-22 21:44:28][INFO][UIAutomation] Clicked center at (1036, 273)[0m
[32m[2025-04-22 21:44:30][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:44:30][INFO][UIAutomation] Clicked at position (1090, 354)[0m
[32m[2025-04-22 21:44:30][INFO][UIAutomation] Clicked center at (1090, 354)[0m
[32m[2025-04-22 21:44:32][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-22 21:44:32][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:34][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-22 21:44:34][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:36][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-22 21:44:36][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:38][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:44:38][INFO][UIAutomation] Clicked at position (1727, 394)[0m
[32m[2025-04-22 21:44:38][INFO][UIAutomation] Clicked center at (1727, 394)[0m
[32m[2025-04-22 21:44:40][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-22 21:44:40][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:42][INFO][UIAutomation] 单击ok[0m
[31m[2025-04-22 21:44:42][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:44][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-22 21:44:45][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:50][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-22 21:44:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:55][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-22 21:44:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:55][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 21:44:57][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 21:44:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:44:57][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 21:44:59][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 21:45:43][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:45:48][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:45:48][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:45:48][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:45:48][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:45:48][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:45:50][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:45:50][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:45:50][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:45:52][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:45:52][INFO][UIAutomation] Clicked at position (974, 293)[0m
[32m[2025-04-22 21:45:52][INFO][UIAutomation] Clicked center at (974, 293)[0m
[32m[2025-04-22 21:45:54][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:45:54][INFO][UIAutomation] Clicked at position (1028, 374)[0m
[32m[2025-04-22 21:45:54][INFO][UIAutomation] Clicked center at (1028, 374)[0m
[32m[2025-04-22 21:45:56][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:45:57][INFO][UIAutomation] Clicked at position (973, 315)[0m
[32m[2025-04-22 21:45:57][INFO][UIAutomation] Clicked center at (973, 315)[0m
[32m[2025-04-22 21:45:59][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:45:59][INFO][UIAutomation] Clicked at position (1205, 476)[0m
[32m[2025-04-22 21:45:59][INFO][UIAutomation] Clicked center at (1205, 476)[0m
[32m[2025-04-22 21:46:01][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:46:01][INFO][UIAutomation] Clicked at position (1507, 317)[0m
[32m[2025-04-22 21:46:01][INFO][UIAutomation] Clicked center at (1507, 317)[0m
[32m[2025-04-22 21:46:03][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:46:03][INFO][UIAutomation] Clicked at position (1684, 679)[0m
[32m[2025-04-22 21:46:03][INFO][UIAutomation] Clicked center at (1684, 679)[0m
[32m[2025-04-22 21:46:05][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:46:05][INFO][UIAutomation] Clicked at position (1667, 711)[0m
[32m[2025-04-22 21:46:05][INFO][UIAutomation] Clicked center at (1667, 711)[0m
[32m[2025-04-22 21:46:07][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:46:08][INFO][UIAutomation] Clicked at position (1638, 845)[0m
[32m[2025-04-22 21:46:08][INFO][UIAutomation] Clicked center at (1638, 845)[0m
[32m[2025-04-22 21:46:10][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:46:10][INFO][UIAutomation] Clicked at position (959, 335)[0m
[32m[2025-04-22 21:46:10][INFO][UIAutomation] Clicked center at (959, 335)[0m
[32m[2025-04-22 21:46:15][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-22 21:46:15][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:46:20][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-22 21:46:20][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:46:20][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 21:46:22][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 21:46:22][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:46:22][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 21:46:24][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 21:46:24][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:46:24][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:50:14][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:50:19][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:50:19][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:50:19][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:50:19][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:50:19][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:50:21][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:50:22][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:50:22][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:50:24][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:50:24][INFO][UIAutomation] Clicked at position (974, 293)[0m
[32m[2025-04-22 21:50:24][INFO][UIAutomation] Clicked center at (974, 293)[0m
[32m[2025-04-22 21:50:26][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 21:50:26][INFO][UIAutomation] Clicked at position (1028, 374)[0m
[32m[2025-04-22 21:50:26][INFO][UIAutomation] Clicked center at (1028, 374)[0m
[32m[2025-04-22 21:50:28][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 21:50:28][INFO][UIAutomation] Clicked at position (973, 315)[0m
[32m[2025-04-22 21:50:28][INFO][UIAutomation] Clicked center at (973, 315)[0m
[32m[2025-04-22 21:50:30][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 21:50:31][INFO][UIAutomation] Clicked at position (1205, 476)[0m
[32m[2025-04-22 21:50:31][INFO][UIAutomation] Clicked center at (1205, 476)[0m
[32m[2025-04-22 21:50:33][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 21:50:33][INFO][UIAutomation] Clicked at position (1507, 317)[0m
[32m[2025-04-22 21:50:33][INFO][UIAutomation] Clicked center at (1507, 317)[0m
[32m[2025-04-22 21:50:35][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:50:35][INFO][UIAutomation] Clicked at position (1684, 679)[0m
[32m[2025-04-22 21:50:35][INFO][UIAutomation] Clicked center at (1684, 679)[0m
[32m[2025-04-22 21:50:37][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 21:50:37][INFO][UIAutomation] Clicked at position (1667, 711)[0m
[32m[2025-04-22 21:50:37][INFO][UIAutomation] Clicked center at (1667, 711)[0m
[32m[2025-04-22 21:50:39][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:50:39][INFO][UIAutomation] Clicked at position (1638, 845)[0m
[32m[2025-04-22 21:50:39][INFO][UIAutomation] Clicked center at (1638, 845)[0m
[32m[2025-04-22 21:50:41][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 21:50:42][INFO][UIAutomation] Clicked at position (959, 335)[0m
[32m[2025-04-22 21:50:42][INFO][UIAutomation] Clicked center at (959, 335)[0m
[32m[2025-04-22 21:50:47][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-22 21:50:47][INFO][UIAutomation] Clicked at position (1475, 337)[0m
[32m[2025-04-22 21:50:47][INFO][UIAutomation] Clicked center at (1475, 337)[0m
[32m[2025-04-22 21:50:52][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-22 21:50:52][INFO][UIAutomation] Clicked at position (1586, 337)[0m
[32m[2025-04-22 21:50:52][INFO][UIAutomation] Clicked center at (1586, 337)[0m
[32m[2025-04-22 21:50:52][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 21:50:54][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 21:50:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:50:54][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 21:50:56][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 21:50:56][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:50:56][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:53:36][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:53:41][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:53:41][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:53:41][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:53:41][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:53:41][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 21:53:43][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 21:53:44][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 21:53:44][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 21:53:46][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 21:53:46][INFO][UIAutomation] Clicked at position (974, 293)[0m
[32m[2025-04-22 21:53:46][INFO][UIAutomation] Clicked center at (974, 293)[0m
[32m[2025-04-22 21:53:48][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-22 21:53:48][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:53:50][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-22 21:53:50][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:53:52][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-22 21:53:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:53:54][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-22 21:53:54][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:53:56][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 21:53:56][INFO][UIAutomation] Clicked at position (1665, 414)[0m
[32m[2025-04-22 21:53:56][INFO][UIAutomation] Clicked center at (1665, 414)[0m
[32m[2025-04-22 21:53:58][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-22 21:53:58][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:54:01][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 21:54:01][INFO][UIAutomation] Clicked at position (1255, 633)[0m
[32m[2025-04-22 21:54:01][INFO][UIAutomation] Clicked center at (1255, 633)[0m
[32m[2025-04-22 21:54:03][INFO][UIAutomation] 单击Connect[0m
[31m[2025-04-22 21:54:03][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:54:08][INFO][UIAutomation] 单击Ring[0m
[31m[2025-04-22 21:54:08][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:54:13][INFO][UIAutomation] 单击Accept_Call[0m
[31m[2025-04-22 21:54:13][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 21:54:13][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 21:54:15][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 21:54:15][ERRO][UIAutomation] Image recognition failed: Failed to read D:/linyu/autotest/Testcode/resource/picture/case2.png because file is missing, has improper permissions, or is an unsupported or invalid format[0m
[32m[2025-04-22 21:54:15][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 21:54:17][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:54:18][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:54:18][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:54:18][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:55:15][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:55:20][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:55:20][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:55:20][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:55:20][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:55:20][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 21:55:20][ERRO][UIAutomation] Image recognition failed: Failed to read D:/linyu/autotest/Testcode/resource/picture/case2.png because file is missing, has improper permissions, or is an unsupported or invalid format[0m
[32m[2025-04-22 21:55:20][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 21:55:22][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:55:23][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:55:23][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:55:23][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 21:57:47][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 21:57:52][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 21:57:52][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 21:57:52][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 21:57:52][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-22 21:57:52][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 21:57:52][INFO][UIAutomation] Clicked at position (208, 366)[0m
[32m[2025-04-22 21:57:52][INFO][UIAutomation] Clicked top-left at (208, 366)[0m
[32m[2025-04-22 21:57:52][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 21:57:54][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 21:57:55][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 21:57:55][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 21:57:55][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 22:00:46][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 22:00:51][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 22:00:51][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 22:00:51][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 22:00:51][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-22 22:00:51][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 22:00:51][INFO][UIAutomation] Clicked at position (208, 366)[0m
[32m[2025-04-22 22:00:51][INFO][UIAutomation] Clicked top-left at (208, 366)[0m
[32m[2025-04-22 22:00:51][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 22:00:53][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 22:00:54][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 22:00:54][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 22:00:54][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 22:01:33][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 22:01:38][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 22:01:38][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 22:01:38][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 22:01:38][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 22:01:38][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-22 22:01:38][INFO][UIAutomation] Clicked at position (208, 366)[0m
[32m[2025-04-22 22:01:38][INFO][UIAutomation] Clicked top-left at (208, 366)[0m
[32m[2025-04-22 22:01:38][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 22:01:41][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 22:01:41][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 22:01:41][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 22:02:41][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 22:02:46][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 22:02:46][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 22:02:46][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 22:02:46][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-22 22:02:46][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 22:02:46][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-22 22:02:46][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-22 22:02:46][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 22:02:48][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 22:02:48][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 22:02:48][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 22:03:20][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 22:03:25][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 22:03:25][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 22:03:25][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 22:03:25][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-22 22:03:25][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 22:03:25][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-22 22:03:25][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-22 22:03:25][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 22:03:27][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 22:03:28][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 22:03:28][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 22:03:28][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 22:04:39][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 22:04:44][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 22:04:44][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 22:04:44][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 22:04:44][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 22:04:44][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 22:04:46][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 22:04:46][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 22:04:46][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 22:04:48][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 22:04:48][INFO][UIAutomation] Clicked at position (974, 293)[0m
[32m[2025-04-22 22:04:48][INFO][UIAutomation] Clicked center at (974, 293)[0m
[32m[2025-04-22 22:04:50][INFO][UIAutomation] 双击蓝牙设备[0m
[31m[2025-04-22 22:04:50][ERRO][UIAutomation] Image recognition failed: Failed to read D:/linyu/autotest/Testcode/resource/picture/test2.png because file is missing, has improper permissions, or is an unsupported or invalid format[0m
[32m[2025-04-22 22:04:52][INFO][UIAutomation] 单击Activated_Profile[0m
[31m[2025-04-22 22:04:52][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 22:04:54][INFO][UIAutomation] 双击HFP_AG[0m
[31m[2025-04-22 22:04:55][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 22:04:57][INFO][UIAutomation] 单击Profile_Settings[0m
[31m[2025-04-22 22:04:57][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 22:04:59][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 22:04:59][INFO][UIAutomation] Clicked at position (1665, 414)[0m
[32m[2025-04-22 22:04:59][INFO][UIAutomation] Clicked center at (1665, 414)[0m
[32m[2025-04-22 22:05:01][INFO][UIAutomation] 单击msbc_path[0m
[31m[2025-04-22 22:05:01][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 22:05:03][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 22:05:03][INFO][UIAutomation] Clicked at position (1255, 633)[0m
[32m[2025-04-22 22:05:03][INFO][UIAutomation] Clicked center at (1255, 633)[0m
[32m[2025-04-22 22:05:05][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 22:05:05][INFO][UIAutomation] Clicked at position (959, 335)[0m
[32m[2025-04-22 22:05:05][INFO][UIAutomation] Clicked center at (959, 335)[0m
[32m[2025-04-22 22:05:10][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-22 22:05:11][INFO][UIAutomation] Clicked at position (1475, 337)[0m
[32m[2025-04-22 22:05:11][INFO][UIAutomation] Clicked center at (1475, 337)[0m
[32m[2025-04-22 22:05:16][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-22 22:05:16][INFO][UIAutomation] Clicked at position (1586, 337)[0m
[32m[2025-04-22 22:05:16][INFO][UIAutomation] Clicked center at (1586, 337)[0m
[32m[2025-04-22 22:05:16][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 22:05:18][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 22:05:18][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 22:05:18][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 22:05:20][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 22:05:21][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 22:05:21][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 22:05:21][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 22:09:48][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 22:09:53][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 22:09:53][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 22:09:53][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 22:09:53][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 22:09:53][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 22:09:55][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 22:09:56][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 22:09:56][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 22:09:58][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 22:09:58][INFO][UIAutomation] Clicked at position (974, 293)[0m
[32m[2025-04-22 22:09:58][INFO][UIAutomation] Clicked center at (974, 293)[0m
[32m[2025-04-22 22:10:00][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 22:10:00][INFO][UIAutomation] Clicked at position (1031, 376)[0m
[32m[2025-04-22 22:10:00][INFO][UIAutomation] Clicked center at (1031, 376)[0m
[32m[2025-04-22 22:10:02][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 22:10:02][INFO][UIAutomation] Clicked at position (973, 315)[0m
[32m[2025-04-22 22:10:02][INFO][UIAutomation] Clicked center at (973, 315)[0m
[32m[2025-04-22 22:10:04][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 22:10:05][INFO][UIAutomation] Clicked at position (1205, 476)[0m
[32m[2025-04-22 22:10:05][INFO][UIAutomation] Clicked center at (1205, 476)[0m
[32m[2025-04-22 22:10:07][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 22:10:07][INFO][UIAutomation] Clicked at position (1507, 317)[0m
[32m[2025-04-22 22:10:07][INFO][UIAutomation] Clicked center at (1507, 317)[0m
[32m[2025-04-22 22:10:09][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 22:10:09][INFO][UIAutomation] Clicked at position (1684, 679)[0m
[32m[2025-04-22 22:10:09][INFO][UIAutomation] Clicked center at (1684, 679)[0m
[32m[2025-04-22 22:10:11][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 22:10:11][INFO][UIAutomation] Clicked at position (1667, 711)[0m
[32m[2025-04-22 22:10:11][INFO][UIAutomation] Clicked center at (1667, 711)[0m
[32m[2025-04-22 22:10:13][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 22:10:13][INFO][UIAutomation] Clicked at position (1638, 845)[0m
[32m[2025-04-22 22:10:13][INFO][UIAutomation] Clicked center at (1638, 845)[0m
[32m[2025-04-22 22:10:15][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 22:10:16][INFO][UIAutomation] Clicked at position (959, 335)[0m
[32m[2025-04-22 22:10:16][INFO][UIAutomation] Clicked center at (959, 335)[0m
[32m[2025-04-22 22:10:21][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-22 22:10:21][INFO][UIAutomation] Clicked at position (1475, 337)[0m
[32m[2025-04-22 22:10:21][INFO][UIAutomation] Clicked center at (1475, 337)[0m
[32m[2025-04-22 22:10:26][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-22 22:10:26][INFO][UIAutomation] Clicked at position (1586, 337)[0m
[32m[2025-04-22 22:10:26][INFO][UIAutomation] Clicked center at (1586, 337)[0m
[32m[2025-04-22 22:10:26][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 22:10:28][INFO][UIAutomation] 运行用例[0m
[31m[2025-04-22 22:10:28][ERRO][UIAutomation] Image recognition failed: [0m
[32m[2025-04-22 22:10:28][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 22:10:30][INFO][UIAutomation] Task completed: test_task1[0m
[33m[2025-04-22 22:10:31][WARN][UIAutomation] Task queue is empty or an error occurred: [0m
[32m[2025-04-22 22:10:31][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 22:10:31][INFO][UIAutomation] UI automation framework finished successfully[0m
[32m[2025-04-22 22:11:10][INFO][UIAutomation] UIAutomationFramework initialized[0m
[32m[2025-04-22 22:11:15][INFO][UIAutomation] Added task: test_task1[0m
[32m[2025-04-22 22:11:15][INFO][UIAutomation] Starting UI automation framework[0m
[32m[2025-04-22 22:11:15][INFO][UIAutomation] Starting test task 1[0m
[32m[2025-04-22 22:11:15][INFO][UIAutomation] F5进入蓝牙选项[0m
[32m[2025-04-22 22:11:15][INFO][UIAutomation] Started 1 worker threads[0m
[32m[2025-04-22 22:11:17][INFO][UIAutomation] 双击进入bluetooth_audio[0m
[32m[2025-04-22 22:11:17][INFO][UIAutomation] Clicked at position (851, 313)[0m
[32m[2025-04-22 22:11:17][INFO][UIAutomation] Clicked center at (851, 313)[0m
[32m[2025-04-22 22:11:19][INFO][UIAutomation] 单击选择蓝牙设备[0m
[32m[2025-04-22 22:11:19][INFO][UIAutomation] Clicked at position (974, 293)[0m
[32m[2025-04-22 22:11:19][INFO][UIAutomation] Clicked center at (974, 293)[0m
[32m[2025-04-22 22:11:21][INFO][UIAutomation] 双击蓝牙设备[0m
[32m[2025-04-22 22:11:22][INFO][UIAutomation] Clicked at position (1031, 376)[0m
[32m[2025-04-22 22:11:22][INFO][UIAutomation] Clicked center at (1031, 376)[0m
[32m[2025-04-22 22:11:24][INFO][UIAutomation] 单击Activated_Profile[0m
[32m[2025-04-22 22:11:24][INFO][UIAutomation] Clicked at position (973, 315)[0m
[32m[2025-04-22 22:11:24][INFO][UIAutomation] Clicked center at (973, 315)[0m
[32m[2025-04-22 22:11:26][INFO][UIAutomation] 双击HFP_AG[0m
[32m[2025-04-22 22:11:26][INFO][UIAutomation] Clicked at position (1205, 476)[0m
[32m[2025-04-22 22:11:26][INFO][UIAutomation] Clicked center at (1205, 476)[0m
[32m[2025-04-22 22:11:28][INFO][UIAutomation] 单击Profile_Settings[0m
[32m[2025-04-22 22:11:28][INFO][UIAutomation] Clicked at position (1507, 317)[0m
[32m[2025-04-22 22:11:28][INFO][UIAutomation] Clicked center at (1507, 317)[0m
[32m[2025-04-22 22:11:30][INFO][UIAutomation] 单击Select_msbccvsd_path[0m
[32m[2025-04-22 22:11:30][INFO][UIAutomation] Clicked at position (1684, 679)[0m
[32m[2025-04-22 22:11:30][INFO][UIAutomation] Clicked center at (1684, 679)[0m
[32m[2025-04-22 22:11:32][INFO][UIAutomation] 单击msbc_path[0m
[32m[2025-04-22 22:11:33][INFO][UIAutomation] Clicked at position (1667, 711)[0m
[32m[2025-04-22 22:11:33][INFO][UIAutomation] Clicked center at (1667, 711)[0m
[32m[2025-04-22 22:11:35][INFO][UIAutomation] 单击ok[0m
[32m[2025-04-22 22:11:35][INFO][UIAutomation] Clicked at position (1638, 845)[0m
[32m[2025-04-22 22:11:35][INFO][UIAutomation] Clicked center at (1638, 845)[0m
[32m[2025-04-22 22:11:37][INFO][UIAutomation] 单击Connect[0m
[32m[2025-04-22 22:11:37][INFO][UIAutomation] Clicked at position (959, 335)[0m
[32m[2025-04-22 22:11:37][INFO][UIAutomation] Clicked center at (959, 335)[0m
[32m[2025-04-22 22:11:42][INFO][UIAutomation] 单击Ring[0m
[32m[2025-04-22 22:11:42][INFO][UIAutomation] Clicked at position (1475, 337)[0m
[32m[2025-04-22 22:11:42][INFO][UIAutomation] Clicked center at (1475, 337)[0m
[32m[2025-04-22 22:11:47][INFO][UIAutomation] 单击Accept_Call[0m
[32m[2025-04-22 22:11:47][INFO][UIAutomation] Clicked at position (1586, 337)[0m
[32m[2025-04-22 22:11:47][INFO][UIAutomation] Clicked center at (1586, 337)[0m
[32m[2025-04-22 22:11:47][INFO][UIAutomation] 跳转用例界面[0m
[32m[2025-04-22 22:11:55][INFO][UIAutomation] 运行用例[0m
[32m[2025-04-22 22:11:55][INFO][UIAutomation] Clicked at position (210, 369)[0m
[32m[2025-04-22 22:11:55][INFO][UIAutomation] Clicked top-left at (210, 369)[0m
[32m[2025-04-22 22:11:55][INFO][UIAutomation] Completed test task 1[0m
[32m[2025-04-22 22:12:00][INFO][UIAutomation] Task completed: test_task1[0m
[32m[2025-04-22 22:12:00][INFO][UIAutomation] All threads stopped[0m
[32m[2025-04-22 22:12:00][INFO][UIAutomation] UI automation framework finished successfully[0m
